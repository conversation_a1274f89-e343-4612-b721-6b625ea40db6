{% extends 'base.html' %}

{% block title %}Server Error - Ventsys{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6 text-center">
        <div class="card shadow">
            <div class="card-body p-5">
                <div class="mb-4">
                    <i class="bi bi-exclamation-octagon text-danger" style="font-size: 5rem;"></i>
                </div>
                
                <h1 class="display-4 text-muted">500</h1>
                <h2 class="mb-3">Internal Server Error</h2>
                
                <p class="lead text-muted mb-4">
                    Something went wrong on our end. We're working to fix this issue.
                </p>
                
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Error ID:</strong> {{ error_id or 'N/A' }}<br>
                    <small class="text-muted">Please include this ID when contacting support.</small>
                </div>
                
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('dashboard.index') if current_user.is_authenticated else url_for('home') }}" 
                       class="btn btn-primary">
                        <i class="bi bi-house me-2"></i>Go Home
                    </a>
                    <button class="btn btn-outline-secondary" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise me-2"></i>Try Again
                    </button>
                </div>
                
                <hr class="my-4">
                
                <div class="text-muted">
                    <p class="mb-2">If this problem persists, please contact our support team:</p>
                    <p>
                        <i class="bi bi-envelope me-1"></i>
                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                        <br>
                        <i class="bi bi-telephone me-1"></i>
                        <a href="tel:******-0123" class="text-decoration-none">+****************</a>
                    </p>
                </div>
            </div>
        </div>
        
        {% if config.DEBUG %}
        <div class="card mt-4 border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0"><i class="bi bi-bug me-2"></i>Debug Information</h6>
            </div>
            <div class="card-body">
                <p class="text-muted small">
                    This information is only visible in debug mode.
                </p>
                {% if error_details %}
                <pre class="bg-light p-3 rounded small">{{ error_details }}</pre>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh after 60 seconds
setTimeout(function() {
    if (confirm('Would you like to try reloading the page?')) {
        location.reload();
    }
}, 60000);

// Report error automatically (if analytics/error tracking is enabled)
if (typeof gtag !== 'undefined') {
    gtag('event', 'exception', {
        'description': 'Server Error 500',
        'fatal': false
    });
}
</script>
{% endblock %}
