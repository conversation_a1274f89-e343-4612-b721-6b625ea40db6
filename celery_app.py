"""
Celery application configuration for Ventsys Fleet Tracking System.

This module sets up Celery for handling asynchronous tasks such as:
- Email notifications
- SMS alerts
- Data processing
- Report generation
- Background maintenance tasks
"""

import os
from celery import Celery
from app.config import config

def make_celery(app_name=__name__):
    """Create and configure Celery instance"""

    # Get configuration
    config_name = os.environ.get('FLASK_CONFIG', 'default')
    app_config = config[config_name]

    # Redis configuration
    redis_url = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

    # Create Celery instance
    celery = Celery(
        app_name,
        broker=redis_url,
        backend=redis_url,
        include=['app.tasks.notifications', 'app.tasks.data_processing', 'app.tasks.reports']
    )

    # Configure Celery
    celery.conf.update(
        # Task routing
        task_routes={
            'app.tasks.notifications.*': {'queue': 'notifications'},
            'app.tasks.data_processing.*': {'queue': 'data_processing'},
            'app.tasks.reports.*': {'queue': 'reports'},
        },

        # Task serialization
        task_serializer='json',
        accept_content=['json'],
        result_serializer='json',
        timezone='UTC',
        enable_utc=True,

        # Task execution
        task_always_eager=False,  # Set to True for testing
        task_eager_propagates=True,
        task_ignore_result=False,

        # Worker configuration
        worker_prefetch_multiplier=1,
        worker_max_tasks_per_child=1000,

        # Task retry configuration
        task_acks_late=True,
        task_reject_on_worker_lost=True,

        # Result backend settings
        result_expires=3600,  # 1 hour
        result_backend_transport_options={
            'master_name': 'mymaster',
            'visibility_timeout': 3600,
        },

        # Beat schedule for periodic tasks
        beat_schedule={
            'cleanup-old-locations': {
                'task': 'app.tasks.data_processing.cleanup_old_locations',
                'schedule': 3600.0,  # Every hour
            },
            'generate-daily-reports': {
                'task': 'app.tasks.reports.generate_daily_reports',
                'schedule': 86400.0,  # Every day
                'options': {'queue': 'reports'}
            },
            'check-vehicle-status': {
                'task': 'app.tasks.data_processing.check_vehicle_status',
                'schedule': 300.0,  # Every 5 minutes
            },
            'send-weekly-reports': {
                'task': 'app.tasks.reports.send_weekly_reports',
                'schedule': 604800.0,  # Every week
                'options': {'queue': 'reports'}
            },
        },

        # Monitoring
        worker_send_task_events=True,
        task_send_sent_event=True,

        # Security
        worker_hijack_root_logger=False,
        worker_log_color=False,
    )

    return celery

# Create Celery instance
celery_app = make_celery()

if __name__ == '__main__':
    celery_app.start()