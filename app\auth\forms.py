from flask_wtf import <PERSON>laskForm
from wtforms import StringField, PasswordField, SelectField, BooleanField, TextAreaField, TelField
from wtforms.validators import (
    DataRequired, Email, Length, Regexp, ValidationError, Optional, EqualTo
)
from wtforms.widgets import PasswordInput
from .models import User
import re

class StrongPasswordValidator:
    """Custom validator for strong passwords"""
    def __init__(self, message=None):
        if not message:
            message = ('Password must be at least 8 characters long and contain '
                      'at least one uppercase letter, one lowercase letter, '
                      'one digit, and one special character.')
        self.message = message

    def __call__(self, form, field):
        password = field.data
        if not password:
            return

        if len(password) < 8:
            raise ValidationError('Password must be at least 8 characters long.')

        if not re.search(r'[A-Z]', password):
            raise ValidationError('Password must contain at least one uppercase letter.')

        if not re.search(r'[a-z]', password):
            raise ValidationError('Password must contain at least one lowercase letter.')

        if not re.search(r'\d', password):
            raise ValidationError('Password must contain at least one digit.')

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError('Password must contain at least one special character.')

class UniqueEmailValidator:
    """Custom validator to check if email is unique"""
    def __init__(self, message=None):
        if not message:
            message = 'This email address is already registered.'
        self.message = message

    def __call__(self, form, field):
        if User.query.filter_by(email=field.data.lower()).first():
            raise ValidationError(self.message)

class LoginForm(FlaskForm):
    email = StringField('Email Address', validators=[
        DataRequired(message='Email is required.'),
        Email(message='Please enter a valid email address.'),
        Length(max=120, message='Email must be less than 120 characters.')
    ], render_kw={'placeholder': 'Enter your email address', 'autocomplete': 'email'})

    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required.')
    ], render_kw={'placeholder': 'Enter your password', 'autocomplete': 'current-password'})

    remember_me = BooleanField('Remember me for 30 days')

class CreateUserForm(FlaskForm):
    email = StringField('Email Address', validators=[
        DataRequired(message='Email is required.'),
        Email(message='Please enter a valid email address.'),
        Length(max=120, message='Email must be less than 120 characters.'),
        UniqueEmailValidator()
    ], render_kw={'placeholder': '<EMAIL>'})

    password = PasswordField('Password', validators=[
        DataRequired(message='Password is required.'),
        StrongPasswordValidator()
    ], render_kw={'placeholder': 'Enter secure password'})

    role = SelectField('Role', choices=[
        ('admin', 'Admin'),
        ('customer', 'Customer')  # Superadmin can't be assigned via form
    ], validators=[DataRequired(message='Please select a role.')])

    send_welcome_email = BooleanField('Send welcome email', default=True)

class ForgotPasswordForm(FlaskForm):
    email = StringField('Email Address', validators=[
        DataRequired(message='Email is required.'),
        Email(message='Please enter a valid email address.'),
        Length(max=120, message='Email must be less than 120 characters.')
    ], render_kw={'placeholder': 'Enter your email address', 'autocomplete': 'email'})

class ResetPasswordForm(FlaskForm):
    password = PasswordField('New Password', validators=[
        DataRequired(message='Password is required.'),
        StrongPasswordValidator()
    ])

    confirm_password = PasswordField('Confirm Password', validators=[
        DataRequired(message='Please confirm your password.'),
        EqualTo('password', message='Passwords must match.')
    ])

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('Current Password', validators=[
        DataRequired(message='Current password is required.')
    ])

    new_password = PasswordField('New Password', validators=[
        DataRequired(message='New password is required.'),
        StrongPasswordValidator()
    ])

    confirm_password = PasswordField('Confirm New Password', validators=[
        DataRequired(message='Please confirm your new password.'),
        EqualTo('new_password', message='Passwords must match.')
    ])

class ProfileForm(FlaskForm):
    first_name = StringField('First Name', validators=[
        Optional(),
        Length(max=50, message='First name must be less than 50 characters.'),
        Regexp(r'^[a-zA-Z\s\-\']*$', message='First name can only contain letters, spaces, hyphens, and apostrophes.')
    ])

    last_name = StringField('Last Name', validators=[
        Optional(),
        Length(max=50, message='Last name must be less than 50 characters.'),
        Regexp(r'^[a-zA-Z\s\-\']*$', message='Last name can only contain letters, spaces, hyphens, and apostrophes.')
    ])

    phone = TelField('Phone Number', validators=[
        Optional(),
        Regexp(r'^\+?1?[-.\s]?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$',
               message='Please enter a valid phone number.')
    ])

    timezone = SelectField('Timezone', choices=[
        ('UTC', 'UTC'),
        ('America/New_York', 'Eastern Time'),
        ('America/Chicago', 'Central Time'),
        ('America/Denver', 'Mountain Time'),
        ('America/Los_Angeles', 'Pacific Time'),
        ('Europe/London', 'London'),
        ('Europe/Paris', 'Paris'),
        ('Asia/Tokyo', 'Tokyo'),
        ('Australia/Sydney', 'Sydney')
    ], default='UTC')

    email_notifications = BooleanField('Email notifications for alerts', default=True)
    sms_notifications = BooleanField('SMS notifications for critical alerts')
    weekly_reports = BooleanField('Weekly activity reports', default=True)