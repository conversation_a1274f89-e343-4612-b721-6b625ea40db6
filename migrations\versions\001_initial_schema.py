"""Initial database schema

Revision ID: 001
Revises: 
Create Date: 2025-01-23 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Create initial database schema."""
    
    # Create users table
    op.create_table('user',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('email', sa.String(length=120), nullable=False),
        sa.Column('password_hash', sa.String(length=128), nullable=False),
        sa.Column('role', sa.String(length=20), nullable=False),
        sa.Column('is_active', sa.<PERSON>an(), nullable=False),
        sa.Column('first_name', sa.String(length=50), nullable=True),
        sa.Column('last_name', sa.String(length=50), nullable=True),
        sa.Column('phone', sa.String(length=20), nullable=True),
        sa.Column('timezone', sa.String(length=50), nullable=True),
        sa.Column('email_notifications', sa.Boolean(), nullable=True),
        sa.Column('sms_notifications', sa.Boolean(), nullable=True),
        sa.Column('weekly_reports', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('last_login', sa.DateTime(), nullable=True),
        sa.Column('last_updated', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for users table
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_index(op.f('ix_user_role'), 'user', ['role'], unique=False)
    
    # Create vehicles table
    op.create_table('vehicle',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=50), nullable=False),
        sa.Column('license_plate', sa.String(length=20), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('last_updated', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for vehicles table
    op.create_index(op.f('ix_vehicle_license_plate'), 'vehicle', ['license_plate'], unique=True)
    
    # Create geofences table
    op.create_table('geofence',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('coordinates', sa.JSON(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('fence_type', sa.String(length=20), nullable=True),
        sa.Column('alert_on_enter', sa.Boolean(), nullable=True),
        sa.Column('alert_on_exit', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('created_by', sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['user.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create locations table
    op.create_table('location',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('vehicle_id', sa.Integer(), nullable=False),
        sa.Column('lat', sa.Float(), nullable=False),
        sa.Column('lng', sa.Float(), nullable=False),
        sa.Column('timestamp', sa.DateTime(), nullable=False),
        sa.Column('speed', sa.Float(), nullable=True),
        sa.Column('heading', sa.Float(), nullable=True),
        sa.Column('accuracy', sa.Float(), nullable=True),
        sa.Column('altitude', sa.Float(), nullable=True),
        sa.Column('is_valid', sa.Boolean(), nullable=True),
        sa.Column('source', sa.String(length=20), nullable=True),
        sa.ForeignKeyConstraint(['vehicle_id'], ['vehicle.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for locations table
    op.create_index(op.f('ix_location_vehicle_id'), 'location', ['vehicle_id'], unique=False)
    op.create_index(op.f('ix_location_timestamp'), 'location', ['timestamp'], unique=False)
    op.create_index('idx_vehicle_timestamp', 'location', ['vehicle_id', 'timestamp'], unique=False)
    op.create_index('idx_timestamp_vehicle', 'location', ['timestamp', 'vehicle_id'], unique=False)
    
    # Create alerts table
    op.create_table('alert',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(length=100), nullable=False),
        sa.Column('message', sa.Text(), nullable=False),
        sa.Column('alert_type', sa.String(length=20), nullable=False),
        sa.Column('vehicle_id', sa.Integer(), nullable=True),
        sa.Column('geofence_id', sa.Integer(), nullable=True),
        sa.Column('user_id', sa.Integer(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('is_read', sa.Boolean(), nullable=False),
        sa.Column('acknowledged_at', sa.DateTime(), nullable=True),
        sa.Column('acknowledged_by', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('expires_at', sa.DateTime(), nullable=True),
        sa.Column('email_sent', sa.Boolean(), nullable=True),
        sa.Column('sms_sent', sa.Boolean(), nullable=True),
        sa.Column('push_sent', sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(['acknowledged_by'], ['user.id'], ),
        sa.ForeignKeyConstraint(['geofence_id'], ['geofence.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
        sa.ForeignKeyConstraint(['vehicle_id'], ['vehicle.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for alerts table
    op.create_index(op.f('ix_alert_alert_type'), 'alert', ['alert_type'], unique=False)
    op.create_index(op.f('ix_alert_vehicle_id'), 'alert', ['vehicle_id'], unique=False)
    op.create_index(op.f('ix_alert_created_at'), 'alert', ['created_at'], unique=False)
    
    # Set default values
    op.execute("UPDATE user SET timezone = 'UTC' WHERE timezone IS NULL")
    op.execute("UPDATE user SET email_notifications = 1 WHERE email_notifications IS NULL")
    op.execute("UPDATE user SET sms_notifications = 0 WHERE sms_notifications IS NULL")
    op.execute("UPDATE user SET weekly_reports = 1 WHERE weekly_reports IS NULL")
    
    op.execute("UPDATE vehicle SET is_active = 1 WHERE is_active IS NULL")
    
    op.execute("UPDATE geofence SET fence_type = 'inclusion' WHERE fence_type IS NULL")
    op.execute("UPDATE geofence SET alert_on_enter = 1 WHERE alert_on_enter IS NULL")
    op.execute("UPDATE geofence SET alert_on_exit = 1 WHERE alert_on_exit IS NULL")
    
    op.execute("UPDATE location SET is_valid = 1 WHERE is_valid IS NULL")
    op.execute("UPDATE location SET source = 'gps' WHERE source IS NULL")
    
    op.execute("UPDATE alert SET is_active = 1 WHERE is_active IS NULL")
    op.execute("UPDATE alert SET is_read = 0 WHERE is_read IS NULL")
    op.execute("UPDATE alert SET email_sent = 0 WHERE email_sent IS NULL")
    op.execute("UPDATE alert SET sms_sent = 0 WHERE sms_sent IS NULL")
    op.execute("UPDATE alert SET push_sent = 0 WHERE push_sent IS NULL")


def downgrade():
    """Drop all tables."""
    op.drop_table('alert')
    op.drop_table('location')
    op.drop_table('geofence')
    op.drop_table('vehicle')
    op.drop_table('user')
