<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Ventsys Fleet Tracking{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <style>
        .navbar-brand {
            font-weight: bold;
        }

        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border: 1px solid rgba(0, 0, 0, 0.125);
        }

        .btn {
            border-radius: 0.375rem;
        }

        .alert {
            border-radius: 0.5rem;
        }

        .table th {
            border-top: none;
            font-weight: 600;
        }

        .vehicle-status {
            font-size: 0.75rem;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .map-container {
            position: relative;
        }

        .map-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1000;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">Ventsys</a>
            {% if current_user.is_authenticated %}
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard.index') }}">Dashboard</a>
                {% if current_user.role in ['admin', 'superadmin'] %}
                <a class="nav-link" href="{{ url_for('tracking.live_map') }}">Live Map</a>
                {% endif %}
                {% if current_user.role == 'superadmin' %}
                <a class="nav-link" href="{{ url_for('auth.create_user') }}">Create User</a>
                {% endif %}
                <a class="nav-link" href="{{ url_for('auth.logout') }}">Logout ({{ current_user.email }})</a>
            </div>
            {% endif %}
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>Ventsys Fleet Tracking</h5>
                    <p class="text-muted">Real-time vehicle monitoring and management system</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="text-muted">
                        &copy; {{ moment().year }} Ventsys. All rights reserved.<br>
                        <small>Version 1.0.0</small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Global utility functions
        function showAlert(message, type = 'info', duration = 3000) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            const container = document.querySelector('.container');
            container.insertBefore(alertDiv, container.firstChild);

            if (duration > 0) {
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, duration);
            }
        }

        // CSRF token for AJAX requests
        function getCSRFToken() {
            const token = document.querySelector('meta[name="csrf-token"]');
            return token ? token.getAttribute('content') : '';
        }

        // Format date/time consistently
        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        // Auto-dismiss alerts after page load
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert:not(.alert-dismissible)');
            alerts.forEach(alert => {
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 5000);
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
