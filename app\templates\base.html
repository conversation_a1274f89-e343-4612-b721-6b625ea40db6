<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Ventsys Fleet Tracking{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('home') }}">Ventsys</a>
            {% if current_user.is_authenticated %}
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="{{ url_for('dashboard.index') }}">Dashboard</a>
                {% if current_user.role in ['admin', 'superadmin'] %}
                <a class="nav-link" href="{{ url_for('tracking.live_map') }}">Live Map</a>
                {% endif %}
                {% if current_user.role == 'superadmin' %}
                <a class="nav-link" href="{{ url_for('auth.create_user') }}">Create User</a>
                {% endif %}
                <a class="nav-link" href="{{ url_for('auth.logout') }}">Logout ({{ current_user.email }})</a>
            </div>
            {% endif %}
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
