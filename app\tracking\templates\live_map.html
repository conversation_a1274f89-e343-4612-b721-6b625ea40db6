{% extends 'base.html' %}

{% block title %}Live Map - Ventsys{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>Live Fleet Map</h1>
        <p class="text-muted">Real-time tracking of active vehicles</p>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5>Active Vehicles</h5>
            </div>
            <div class="card-body">
                <div class="list-group" id="vehicle-list">
                    {% for vehicle in vehicles %}
                    <div class="list-group-item d-flex justify-content-between align-items-center"
                         data-vehicle-id="{{ vehicle.id }}"
                         data-plate="{{ vehicle.license_plate }}">
                        <div>
                            <h6 class="mb-1">{{ vehicle.name }}</h6>
                            <small class="text-muted">{{ vehicle.license_plate }}</small>
                            {% if vehicle.last_updated %}
                            <br><small class="text-success">Last seen: {{ vehicle.last_updated.strftime('%H:%M') }}</small>
                            {% else %}
                            <br><small class="text-warning">No recent data</small>
                            {% endif %}
                        </div>
                        <span class="badge bg-success rounded-pill vehicle-status" id="status-{{ vehicle.id }}">Online</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5>Map Controls</h5>
            </div>
            <div class="card-body">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                    <label class="form-check-label" for="autoRefresh">
                        Auto Refresh (30s)
                    </label>
                </div>
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="showTrails">
                    <label class="form-check-label" for="showTrails">
                        Show Vehicle Trails
                    </label>
                </div>
                <button class="btn btn-primary btn-sm mt-2" onclick="centerMap()">Center Map</button>
                <button class="btn btn-secondary btn-sm mt-2" onclick="refreshData()">Refresh Now</button>
            </div>
        </div>
    </div>

    <div class="col-md-9">
        <div class="card">
            <div class="card-body p-0">
                <div id="map" style="height: 600px; width: 100%;"></div>
            </div>
        </div>
    </div>
</div>

<!-- Vehicle Details Modal -->
<div class="modal fade" id="vehicleModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="vehicleModalTitle">Vehicle Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="vehicleModalBody">
                <!-- Vehicle details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <a href="#" class="btn btn-primary" id="viewHistoryBtn">View History</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let map;
let vehicleMarkers = {};
let vehicleTrails = {};
let autoRefreshInterval;

// Initialize map
function initMap() {
    map = L.map('map').setView([40.7128, -74.0060], 10); // Default to NYC

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Load initial vehicle data
    loadVehicleData();

    // Set up auto refresh
    setupAutoRefresh();
}

// Load vehicle location data
function loadVehicleData() {
    fetch('/tracking/api/vehicles/locations')
        .then(response => response.json())
        .then(data => {
            updateVehicleMarkers(data);
            updateVehicleList(data);
        })
        .catch(error => {
            console.error('Error loading vehicle data:', error);
            showAlert('Error loading vehicle data', 'danger');
        });
}

// Update vehicle markers on map
function updateVehicleMarkers(vehicles) {
    // Clear existing markers
    Object.values(vehicleMarkers).forEach(marker => map.removeLayer(marker));
    vehicleMarkers = {};

    vehicles.forEach(vehicle => {
        if (vehicle.lat && vehicle.lng) {
            const marker = L.marker([vehicle.lat, vehicle.lng])
                .bindPopup(`
                    <strong>${vehicle.name}</strong><br>
                    Plate: ${vehicle.license_plate}<br>
                    Speed: ${vehicle.speed || 'N/A'} km/h<br>
                    Last Update: ${vehicle.last_updated || 'N/A'}
                `)
                .on('click', () => showVehicleDetails(vehicle.id));

            marker.addTo(map);
            vehicleMarkers[vehicle.id] = marker;

            // Add trail if enabled
            if (document.getElementById('showTrails').checked) {
                updateVehicleTrail(vehicle.id);
            }
        }
    });

    // Auto-fit map to show all vehicles
    if (Object.keys(vehicleMarkers).length > 0) {
        const group = new L.featureGroup(Object.values(vehicleMarkers));
        map.fitBounds(group.getBounds().pad(0.1));
    }
}

// Update vehicle list in sidebar
function updateVehicleList(vehicles) {
    vehicles.forEach(vehicle => {
        const statusElement = document.getElementById(`status-${vehicle.id}`);
        if (statusElement) {
            const timeDiff = vehicle.last_updated ?
                (new Date() - new Date(vehicle.last_updated)) / 1000 / 60 : null;

            if (!timeDiff || timeDiff > 30) {
                statusElement.textContent = 'Offline';
                statusElement.className = 'badge bg-danger rounded-pill vehicle-status';
            } else if (timeDiff > 10) {
                statusElement.textContent = 'Idle';
                statusElement.className = 'badge bg-warning rounded-pill vehicle-status';
            } else {
                statusElement.textContent = 'Online';
                statusElement.className = 'badge bg-success rounded-pill vehicle-status';
            }
        }
    });
}

// Show vehicle details modal
function showVehicleDetails(vehicleId) {
    fetch(`/tracking/api/vehicles/${vehicleId}/details`)
        .then(response => response.json())
        .then(vehicle => {
            document.getElementById('vehicleModalTitle').textContent = vehicle.name;
            document.getElementById('vehicleModalBody').innerHTML = `
                <table class="table">
                    <tr><td><strong>License Plate:</strong></td><td>${vehicle.license_plate}</td></tr>
                    <tr><td><strong>Current Speed:</strong></td><td>${vehicle.speed || 'N/A'} km/h</td></tr>
                    <tr><td><strong>Last Update:</strong></td><td>${vehicle.last_updated || 'N/A'}</td></tr>
                    <tr><td><strong>Status:</strong></td><td>${vehicle.is_active ? 'Active' : 'Inactive'}</td></tr>
                </table>
            `;
            document.getElementById('viewHistoryBtn').href = `/tracking/history/${vehicleId}`;

            new bootstrap.Modal(document.getElementById('vehicleModal')).show();
        })
        .catch(error => {
            console.error('Error loading vehicle details:', error);
            showAlert('Error loading vehicle details', 'danger');
        });
}

// Setup auto refresh
function setupAutoRefresh() {
    const autoRefreshCheckbox = document.getElementById('autoRefresh');

    function startAutoRefresh() {
        if (autoRefreshInterval) clearInterval(autoRefreshInterval);
        autoRefreshInterval = setInterval(loadVehicleData, 30000); // 30 seconds
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    autoRefreshCheckbox.addEventListener('change', function() {
        if (this.checked) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    });

    // Start auto refresh if enabled
    if (autoRefreshCheckbox.checked) {
        startAutoRefresh();
    }
}

// Utility functions
function centerMap() {
    if (Object.keys(vehicleMarkers).length > 0) {
        const group = new L.featureGroup(Object.values(vehicleMarkers));
        map.fitBounds(group.getBounds().pad(0.1));
    }
}

function refreshData() {
    loadVehicleData();
    showAlert('Data refreshed', 'success');
}

function showAlert(message, type) {
    // Create and show bootstrap alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto dismiss after 3 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// Initialize map when page loads
document.addEventListener('DOMContentLoaded', initMap);
</script>
{% endblock %}