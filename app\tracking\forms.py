from flask_wtf import <PERSON>laskForm
from wtforms import StringField, FloatField, IntegerField, SelectField, DateField, TimeField, TextAreaField, BooleanField
from wtforms.validators import DataRequired, NumberRange, Length, Optional, ValidationError, Regexp
from wtforms.widgets import TextArea
from datetime import datetime, date
import json

class CoordinateValidator:
    """Custom validator for GPS coordinates"""
    def __init__(self, coord_type, message=None):
        self.coord_type = coord_type
        if not message:
            if coord_type == 'latitude':
                message = 'Latitude must be between -90 and 90 degrees.'
            else:
                message = 'Longitude must be between -180 and 180 degrees.'
        self.message = message

    def __call__(self, form, field):
        if field.data is None:
            return
        
        if self.coord_type == 'latitude':
            if not (-90 <= field.data <= 90):
                raise ValidationError(self.message)
        else:  # longitude
            if not (-180 <= field.data <= 180):
                raise ValidationError(self.message)

class JSONValidator:
    """Custom validator for JSON fields"""
    def __init__(self, message=None):
        if not message:
            message = 'Invalid JSON format.'
        self.message = message

    def __call__(self, form, field):
        if not field.data:
            return
        
        try:
            json.loads(field.data)
        except (json.JSONDecodeError, TypeError):
            raise ValidationError(self.message)

class VehicleForm(FlaskForm):
    name = StringField('Vehicle Name', validators=[
        DataRequired(message='Vehicle name is required.'),
        Length(min=2, max=50, message='Vehicle name must be between 2 and 50 characters.'),
        Regexp(r'^[a-zA-Z0-9\s\-_]+$', message='Vehicle name can only contain letters, numbers, spaces, hyphens, and underscores.')
    ])
    
    license_plate = StringField('License Plate', validators=[
        DataRequired(message='License plate is required.'),
        Length(min=2, max=20, message='License plate must be between 2 and 20 characters.'),
        Regexp(r'^[A-Z0-9\-\s]+$', message='License plate can only contain uppercase letters, numbers, hyphens, and spaces.')
    ])
    
    is_active = BooleanField('Active', default=True)
    
    description = TextAreaField('Description', validators=[
        Optional(),
        Length(max=500, message='Description must be less than 500 characters.')
    ])

class GPSDataForm(FlaskForm):
    """Form for validating GPS data submissions"""
    plate = StringField('License Plate', validators=[
        DataRequired(message='License plate is required.'),
        Length(min=2, max=20, message='License plate must be between 2 and 20 characters.')
    ])
    
    lat = FloatField('Latitude', validators=[
        DataRequired(message='Latitude is required.'),
        CoordinateValidator('latitude')
    ])
    
    lng = FloatField('Longitude', validators=[
        DataRequired(message='Longitude is required.'),
        CoordinateValidator('longitude')
    ])
    
    speed = FloatField('Speed (km/h)', validators=[
        Optional(),
        NumberRange(min=0, max=500, message='Speed must be between 0 and 500 km/h.')
    ])
    
    heading = FloatField('Heading (degrees)', validators=[
        Optional(),
        NumberRange(min=0, max=360, message='Heading must be between 0 and 360 degrees.')
    ])
    
    accuracy = FloatField('GPS Accuracy (meters)', validators=[
        Optional(),
        NumberRange(min=0, max=1000, message='Accuracy must be between 0 and 1000 meters.')
    ])

class GeofenceForm(FlaskForm):
    name = StringField('Geofence Name', validators=[
        DataRequired(message='Geofence name is required.'),
        Length(min=2, max=50, message='Geofence name must be between 2 and 50 characters.')
    ])
    
    coordinates = TextAreaField('Coordinates (JSON)', validators=[
        DataRequired(message='Coordinates are required.'),
        JSONValidator()
    ], widget=TextArea())
    
    is_active = BooleanField('Active', default=True)
    
    description = TextAreaField('Description', validators=[
        Optional(),
        Length(max=500, message='Description must be less than 500 characters.')
    ])

class LocationHistoryFilterForm(FlaskForm):
    """Form for filtering location history"""
    date_from = DateField('From Date', validators=[Optional()])
    date_to = DateField('To Date', validators=[Optional()])
    time_from = TimeField('From Time', validators=[Optional()])
    time_to = TimeField('To Time', validators=[Optional()])
    
    def validate_date_to(self, field):
        if field.data and self.date_from.data:
            if field.data < self.date_from.data:
                raise ValidationError('End date must be after start date.')
    
    def validate_time_to(self, field):
        if (field.data and self.time_from.data and 
            self.date_from.data and self.date_to.data and
            self.date_from.data == self.date_to.data):
            if field.data <= self.time_from.data:
                raise ValidationError('End time must be after start time.')

class AlertForm(FlaskForm):
    """Form for creating alerts"""
    title = StringField('Alert Title', validators=[
        DataRequired(message='Alert title is required.'),
        Length(min=5, max=100, message='Alert title must be between 5 and 100 characters.')
    ])
    
    message = TextAreaField('Alert Message', validators=[
        DataRequired(message='Alert message is required.'),
        Length(min=10, max=1000, message='Alert message must be between 10 and 1000 characters.')
    ])
    
    alert_type = SelectField('Alert Type', choices=[
        ('info', 'Information'),
        ('warning', 'Warning'),
        ('critical', 'Critical'),
        ('geofence', 'Geofence Violation'),
        ('speed', 'Speed Violation'),
        ('maintenance', 'Maintenance Required')
    ], validators=[DataRequired(message='Please select an alert type.')])
    
    vehicle_id = IntegerField('Vehicle ID', validators=[
        Optional(),
        NumberRange(min=1, message='Vehicle ID must be a positive number.')
    ])
    
    is_active = BooleanField('Active', default=True)

class ReportForm(FlaskForm):
    """Form for generating reports"""
    report_type = SelectField('Report Type', choices=[
        ('daily', 'Daily Summary'),
        ('weekly', 'Weekly Summary'),
        ('monthly', 'Monthly Summary'),
        ('custom', 'Custom Date Range'),
        ('vehicle', 'Vehicle Report'),
        ('fleet', 'Fleet Overview')
    ], validators=[DataRequired(message='Please select a report type.')])
    
    date_from = DateField('From Date', validators=[
        DataRequired(message='Start date is required.')
    ])
    
    date_to = DateField('To Date', validators=[
        DataRequired(message='End date is required.')
    ])
    
    vehicle_ids = StringField('Vehicle IDs (comma-separated)', validators=[
        Optional(),
        Regexp(r'^[\d,\s]*$', message='Vehicle IDs must be numbers separated by commas.')
    ])
    
    include_charts = BooleanField('Include Charts', default=True)
    include_maps = BooleanField('Include Maps', default=True)
    
    def validate_date_to(self, field):
        if field.data and self.date_from.data:
            if field.data < self.date_from.data:
                raise ValidationError('End date must be after start date.')
            
            # Limit report range to 1 year
            if (field.data - self.date_from.data).days > 365:
                raise ValidationError('Report date range cannot exceed 365 days.')

class SearchForm(FlaskForm):
    """Form for searching vehicles and locations"""
    query = StringField('Search Query', validators=[
        DataRequired(message='Search query is required.'),
        Length(min=2, max=100, message='Search query must be between 2 and 100 characters.')
    ])
    
    search_type = SelectField('Search Type', choices=[
        ('all', 'All'),
        ('vehicles', 'Vehicles'),
        ('locations', 'Locations'),
        ('alerts', 'Alerts')
    ], default='all')
    
    date_range = SelectField('Date Range', choices=[
        ('all', 'All Time'),
        ('today', 'Today'),
        ('week', 'This Week'),
        ('month', 'This Month'),
        ('custom', 'Custom Range')
    ], default='all')

class BulkActionForm(FlaskForm):
    """Form for bulk actions on vehicles"""
    action = SelectField('Action', choices=[
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
        ('delete', 'Delete'),
        ('export', 'Export Data')
    ], validators=[DataRequired(message='Please select an action.')])
    
    vehicle_ids = StringField('Vehicle IDs', validators=[
        DataRequired(message='Please select vehicles.'),
        Regexp(r'^[\d,\s]+$', message='Vehicle IDs must be numbers separated by commas.')
    ])
    
    confirm = BooleanField('I confirm this action', validators=[
        DataRequired(message='Please confirm the action.')
    ])
