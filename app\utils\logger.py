"""
Logging utilities for the Ventsys application.

This module provides convenient logging functions and decorators
for consistent logging throughout the application.
"""

import logging
import functools
import time
from typing import Any, Callable, Dict, Optional
from flask import current_app, request, g
from flask_login import current_user

class VentsysLogger:
    """Enhanced logger with context and structured logging capabilities."""
    
    def __init__(self, name: str = None):
        """
        Initialize the logger.
        
        Args:
            name: Logger name (defaults to calling module)
        """
        self.logger = logging.getLogger(name or __name__)
    
    def _add_context(self, extra_fields: Dict[str, Any] = None) -> Dict[str, Any]:
        """Add context information to log entries."""
        context = {
            'extra_fields': extra_fields or {}
        }
        
        # Add request context if available
        try:
            if request:
                context['extra_fields'].update({
                    'request_id': getattr(g, 'request_id', None),
                    'method': request.method,
                    'endpoint': request.endpoint,
                    'remote_addr': request.remote_addr
                })
                
                # Add user context if authenticated
                if current_user and current_user.is_authenticated:
                    context['extra_fields'].update({
                        'user_id': current_user.id,
                        'user_email': current_user.email,
                        'user_role': current_user.role
                    })
        except RuntimeError:
            # Outside request context
            pass
        
        return context
    
    def debug(self, message: str, **kwargs):
        """Log debug message with context."""
        self.logger.debug(message, extra=self._add_context(kwargs))
    
    def info(self, message: str, **kwargs):
        """Log info message with context."""
        self.logger.info(message, extra=self._add_context(kwargs))
    
    def warning(self, message: str, **kwargs):
        """Log warning message with context."""
        self.logger.warning(message, extra=self._add_context(kwargs))
    
    def error(self, message: str, exc_info: bool = False, **kwargs):
        """Log error message with context."""
        self.logger.error(message, exc_info=exc_info, extra=self._add_context(kwargs))
    
    def critical(self, message: str, exc_info: bool = False, **kwargs):
        """Log critical message with context."""
        self.logger.critical(message, exc_info=exc_info, extra=self._add_context(kwargs))
    
    def security(self, event_type: str, message: str, **kwargs):
        """Log security event."""
        from app.utils.logging_config import log_security_event
        log_security_event(event_type, message, **kwargs)
    
    def performance(self, metric_name: str, value: float, unit: str = 'ms', **kwargs):
        """Log performance metric."""
        from app.utils.logging_config import log_performance_metric
        log_performance_metric(metric_name, value, unit, **kwargs)

# Global logger instance
logger = VentsysLogger('ventsys')

def get_logger(name: str = None) -> VentsysLogger:
    """
    Get a logger instance for a specific module.
    
    Args:
        name: Logger name
        
    Returns:
        VentsysLogger instance
    """
    return VentsysLogger(name)

def log_function_call(include_args: bool = False, include_result: bool = False):
    """
    Decorator to log function calls.
    
    Args:
        include_args: Whether to include function arguments in log
        include_result: Whether to include function result in log
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func_logger = get_logger(func.__module__)
            func_name = f"{func.__module__}.{func.__name__}"
            
            # Log function entry
            log_data = {'function': func_name}
            if include_args:
                log_data['args'] = str(args)
                log_data['kwargs'] = str(kwargs)
            
            func_logger.debug(f"Entering function: {func_name}", **log_data)
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                
                # Log successful completion
                duration = (time.time() - start_time) * 1000
                log_data['duration_ms'] = round(duration, 2)
                
                if include_result:
                    log_data['result'] = str(result)
                
                func_logger.debug(f"Function completed: {func_name}", **log_data)
                
                # Log performance if slow
                if duration > 1000:  # More than 1 second
                    func_logger.performance(f"function_duration.{func_name}", duration)
                
                return result
                
            except Exception as e:
                # Log exception
                duration = (time.time() - start_time) * 1000
                log_data.update({
                    'duration_ms': round(duration, 2),
                    'exception_type': type(e).__name__,
                    'exception_message': str(e)
                })
                
                func_logger.error(f"Function failed: {func_name}", exc_info=True, **log_data)
                raise
        
        return wrapper
    return decorator

def log_database_operation(operation_type: str):
    """
    Decorator to log database operations.
    
    Args:
        operation_type: Type of database operation (CREATE, READ, UPDATE, DELETE)
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            db_logger = get_logger('database')
            func_name = f"{func.__module__}.{func.__name__}"
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                
                duration = (time.time() - start_time) * 1000
                db_logger.info(
                    f"Database operation completed: {operation_type}",
                    operation_type=operation_type,
                    function=func_name,
                    duration_ms=round(duration, 2)
                )
                
                # Log performance metric
                db_logger.performance(f"db_operation.{operation_type.lower()}", duration)
                
                return result
                
            except Exception as e:
                duration = (time.time() - start_time) * 1000
                db_logger.error(
                    f"Database operation failed: {operation_type}",
                    exc_info=True,
                    operation_type=operation_type,
                    function=func_name,
                    duration_ms=round(duration, 2),
                    exception_type=type(e).__name__
                )
                raise
        
        return wrapper
    return decorator

def log_api_call(api_name: str):
    """
    Decorator to log external API calls.
    
    Args:
        api_name: Name of the external API
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            api_logger = get_logger('api')
            func_name = f"{func.__module__}.{func.__name__}"
            
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                
                duration = (time.time() - start_time) * 1000
                api_logger.info(
                    f"API call completed: {api_name}",
                    api_name=api_name,
                    function=func_name,
                    duration_ms=round(duration, 2)
                )
                
                # Log performance metric
                api_logger.performance(f"api_call.{api_name}", duration)
                
                return result
                
            except Exception as e:
                duration = (time.time() - start_time) * 1000
                api_logger.error(
                    f"API call failed: {api_name}",
                    exc_info=True,
                    api_name=api_name,
                    function=func_name,
                    duration_ms=round(duration, 2),
                    exception_type=type(e).__name__
                )
                raise
        
        return wrapper
    return decorator

def log_user_action(action_type: str, resource: str = None):
    """
    Decorator to log user actions for audit trail.
    
    Args:
        action_type: Type of action (CREATE, UPDATE, DELETE, VIEW, etc.)
        resource: Resource being acted upon
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            audit_logger = get_logger('audit')
            
            try:
                # Get user context
                user_info = {}
                if current_user and current_user.is_authenticated:
                    user_info = {
                        'user_id': current_user.id,
                        'user_email': current_user.email,
                        'user_role': current_user.role
                    }
                
                # Execute function
                result = func(*args, **kwargs)
                
                # Log successful action
                audit_logger.info(
                    f"User action: {action_type}",
                    action_type=action_type,
                    resource=resource,
                    function=f"{func.__module__}.{func.__name__}",
                    **user_info
                )
                
                return result
                
            except Exception as e:
                # Log failed action
                audit_logger.warning(
                    f"User action failed: {action_type}",
                    action_type=action_type,
                    resource=resource,
                    function=f"{func.__module__}.{func.__name__}",
                    exception_type=type(e).__name__,
                    **user_info
                )
                raise
        
        return wrapper
    return decorator

# Convenience functions
def log_login_attempt(email: str, success: bool, ip_address: str = None):
    """Log login attempt for security monitoring."""
    security_logger = get_logger('security')
    
    event_type = 'login_success' if success else 'login_failure'
    message = f"Login {'successful' if success else 'failed'} for {email}"
    
    security_logger.security(
        event_type,
        message,
        email=email,
        ip_address=ip_address or (request.remote_addr if request else 'unknown')
    )

def log_permission_denied(resource: str, required_permission: str):
    """Log permission denied events."""
    security_logger = get_logger('security')
    
    user_info = {}
    if current_user and current_user.is_authenticated:
        user_info = {
            'user_id': current_user.id,
            'user_email': current_user.email,
            'user_role': current_user.role
        }
    
    security_logger.security(
        'permission_denied',
        f"Access denied to {resource}",
        resource=resource,
        required_permission=required_permission,
        **user_info
    )

def log_data_export(export_type: str, record_count: int, file_size: int = None):
    """Log data export operations."""
    audit_logger = get_logger('audit')
    
    user_info = {}
    if current_user and current_user.is_authenticated:
        user_info = {
            'user_id': current_user.id,
            'user_email': current_user.email
        }
    
    audit_logger.info(
        f"Data export: {export_type}",
        export_type=export_type,
        record_count=record_count,
        file_size=file_size,
        **user_info
    )
