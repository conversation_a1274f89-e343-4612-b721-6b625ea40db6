#!/usr/bin/env python3
"""
Migration testing script for Ventsys Flask application.

This script tests database migrations to ensure they work correctly
in both upgrade and downgrade directions.
"""

import os
import sys
import tempfile
import shutil
from contextlib import contextmanager
from flask import Flask
from flask_migrate import upgrade, downgrade, current
from app import create_app
from app.extensions import db

class MigrationTester:
    """Test database migrations."""
    
    def __init__(self):
        self.app = None
        self.temp_db = None
    
    def setup_test_environment(self):
        """Set up a temporary test environment."""
        # Create temporary database file
        self.temp_db = tempfile.NamedTemporaryFile(suffix='.db', delete=False)
        self.temp_db.close()
        
        # Set environment for testing
        os.environ['FLASK_CONFIG'] = 'testing'
        os.environ['DATABASE_URL'] = f'sqlite:///{self.temp_db.name}'
        
        # Create test app
        self.app = create_app()
        
        print(f"Test database created: {self.temp_db.name}")
    
    def cleanup_test_environment(self):
        """Clean up test environment."""
        if self.temp_db and os.path.exists(self.temp_db.name):
            os.unlink(self.temp_db.name)
            print("Test database cleaned up.")
    
    @contextmanager
    def app_context(self):
        """Provide app context for database operations."""
        with self.app.app_context():
            yield
    
    def test_fresh_migration(self):
        """Test applying all migrations to a fresh database."""
        print("\n=== Testing Fresh Migration ===")
        
        with self.app_context():
            try:
                # Apply all migrations
                upgrade()
                
                # Check current revision
                rev = current()
                print(f"Current revision after upgrade: {rev}")
                
                # Verify tables exist
                inspector = db.inspect(db.engine)
                tables = inspector.get_table_names()
                
                expected_tables = ['user', 'vehicle', 'location', 'geofence', 'alert', 'alembic_version']
                missing_tables = set(expected_tables) - set(tables)
                
                if missing_tables:
                    print(f"ERROR: Missing tables: {missing_tables}")
                    return False
                else:
                    print("✓ All expected tables created successfully")
                
                # Test basic data operations
                self.test_basic_operations()
                
                return True
                
            except Exception as e:
                print(f"ERROR in fresh migration: {e}")
                return False
    
    def test_migration_rollback(self):
        """Test rolling back migrations."""
        print("\n=== Testing Migration Rollback ===")
        
        with self.app_context():
            try:
                # Get current revision
                current_rev = current()
                print(f"Current revision before rollback: {current_rev}")
                
                # Rollback one step
                downgrade(revision='-1')
                
                # Check new revision
                new_rev = current()
                print(f"Current revision after rollback: {new_rev}")
                
                # Upgrade back
                upgrade()
                
                # Verify we're back to the original state
                final_rev = current()
                if final_rev == current_rev:
                    print("✓ Rollback and re-upgrade successful")
                    return True
                else:
                    print(f"ERROR: Expected {current_rev}, got {final_rev}")
                    return False
                
            except Exception as e:
                print(f"ERROR in migration rollback: {e}")
                return False
    
    def test_basic_operations(self):
        """Test basic database operations."""
        print("\n--- Testing Basic Operations ---")
        
        try:
            # Test user creation
            from app.auth.models import User
            
            user = User(
                email='<EMAIL>',
                role='admin',
                is_active=True
            )
            user.set_password('testpass')
            
            db.session.add(user)
            db.session.commit()
            
            # Verify user was created
            retrieved_user = User.query.filter_by(email='<EMAIL>').first()
            if retrieved_user and retrieved_user.check_password('testpass'):
                print("✓ User creation and authentication working")
            else:
                print("ERROR: User creation or authentication failed")
                return False
            
            # Test vehicle creation
            from app.tracking.models import Vehicle
            
            vehicle = Vehicle(
                name='Test Vehicle',
                license_plate='TEST-001',
                is_active=True
            )
            
            db.session.add(vehicle)
            db.session.commit()
            
            # Verify vehicle was created
            retrieved_vehicle = Vehicle.query.filter_by(license_plate='TEST-001').first()
            if retrieved_vehicle:
                print("✓ Vehicle creation working")
            else:
                print("ERROR: Vehicle creation failed")
                return False
            
            # Test location creation
            from app.tracking.models import Location
            from datetime import datetime
            
            location = Location(
                vehicle_id=vehicle.id,
                lat=40.7128,
                lng=-74.0060,
                timestamp=datetime.utcnow(),
                speed=30.0
            )
            
            db.session.add(location)
            db.session.commit()
            
            # Verify location was created
            retrieved_location = Location.query.filter_by(vehicle_id=vehicle.id).first()
            if retrieved_location:
                print("✓ Location creation working")
            else:
                print("ERROR: Location creation failed")
                return False
            
            return True
            
        except Exception as e:
            print(f"ERROR in basic operations: {e}")
            return False
    
    def test_data_integrity(self):
        """Test data integrity after migrations."""
        print("\n=== Testing Data Integrity ===")
        
        with self.app_context():
            try:
                # Check foreign key constraints
                from app.tracking.models import Location, Vehicle
                from app.auth.models import User
                
                # Get test data
                user = User.query.first()
                vehicle = Vehicle.query.first()
                location = Location.query.first()
                
                if not all([user, vehicle, location]):
                    print("ERROR: Test data not found")
                    return False
                
                # Verify relationships
                if location.vehicle_id == vehicle.id:
                    print("✓ Foreign key relationships working")
                else:
                    print("ERROR: Foreign key relationship broken")
                    return False
                
                # Test cascade behavior (if implemented)
                # This would depend on your specific cascade settings
                
                return True
                
            except Exception as e:
                print(f"ERROR in data integrity test: {e}")
                return False
    
    def run_all_tests(self):
        """Run all migration tests."""
        print("Starting Migration Tests...")
        
        try:
            self.setup_test_environment()
            
            tests = [
                self.test_fresh_migration,
                self.test_migration_rollback,
                self.test_data_integrity
            ]
            
            results = []
            for test in tests:
                result = test()
                results.append(result)
            
            # Summary
            print("\n" + "="*50)
            print("MIGRATION TEST SUMMARY")
            print("="*50)
            
            passed = sum(results)
            total = len(results)
            
            print(f"Tests passed: {passed}/{total}")
            
            if passed == total:
                print("✓ All migration tests PASSED")
                return True
            else:
                print("✗ Some migration tests FAILED")
                return False
        
        finally:
            self.cleanup_test_environment()

def main():
    """Main entry point."""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print(__doc__)
        return
    
    tester = MigrationTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
