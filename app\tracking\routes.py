from flask import Blueprint, request, jsonify, render_template, Response, current_app, flash
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app.extensions import db
from .models import Vehicle, Location, Geofence
from .services import check_geofence
from .forms import GPSDataForm, LocationHistoryFilterForm
from app.auth.decorators import admin_required
import csv
import io
import json
import logging

tracking_bp = Blueprint('tracking', __name__)

# --- Device Communication ---
@tracking_bp.route('/api/gps', methods=['POST'])
def handle_gps_update():
    """Handle GPS data submission from devices with validation"""
    try:
        # Validate content type
        if not request.is_json:
            current_app.logger.warning('GPS update received with invalid content type')
            return jsonify({"error": "Content-Type must be application/json"}), 400

        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # Create form for validation
        form = GPSDataForm(data=data)

        if not form.validate():
            current_app.logger.warning(f'Invalid GPS data received: {form.errors}')
            return jsonify({"error": "Invalid data", "details": form.errors}), 400

        # Find vehicle
        vehicle = Vehicle.query.filter_by(license_plate=form.plate.data.upper()).first()
        if not vehicle:
            current_app.logger.warning(f'GPS update for unknown vehicle: {form.plate.data}')
            return jsonify({"error": "Vehicle not found"}), 404

        if not vehicle.is_active:
            current_app.logger.warning(f'GPS update for inactive vehicle: {form.plate.data}')
            return jsonify({"error": "Vehicle is inactive"}), 403

        # Save location with validation
        location = Location(
            vehicle_id=vehicle.id,
            lat=form.lat.data,
            lng=form.lng.data,
            speed=form.speed.data,
            heading=form.heading.data,
            accuracy=form.accuracy.data,
            timestamp=datetime.utcnow()
        )

        db.session.add(location)

        # Update vehicle last seen
        vehicle.last_updated = datetime.utcnow()

        db.session.commit()

        # Check geofence violations
        try:
            check_geofence(form.lat.data, form.lng.data, vehicle.id)
        except Exception as e:
            current_app.logger.error(f'Geofence check error: {str(e)}')
            # Don't fail the GPS update if geofence check fails

        current_app.logger.debug(f'GPS update successful for vehicle {vehicle.license_plate}')
        return jsonify({
            "status": "success",
            "vehicle_id": vehicle.id,
            "timestamp": location.timestamp.isoformat()
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'GPS update error: {str(e)}')
        return jsonify({"error": "Internal server error"}), 500

# --- Web Interface ---
@tracking_bp.route('/map')
@login_required
@admin_required
def live_map():
    vehicles = Vehicle.query.filter_by(is_active=True).all()
    return render_template('tracking/live_map.html', vehicles=vehicles)

@tracking_bp.route('/history/<int:vehicle_id>')
@login_required
def route_history(vehicle_id):
    vehicle = Vehicle.query.get_or_404(vehicle_id)
    return render_template('tracking/history.html', vehicle=vehicle)

# --- API Endpoints for AJAX ---
@tracking_bp.route('/api/vehicles/locations')
@login_required
@admin_required
def get_vehicle_locations():
    """Get current locations of all active vehicles"""
    vehicles = Vehicle.query.filter_by(is_active=True).all()
    vehicle_data = []

    for vehicle in vehicles:
        # Get latest location
        latest_location = Location.query.filter_by(vehicle_id=vehicle.id)\
            .order_by(Location.timestamp.desc()).first()

        vehicle_info = {
            'id': vehicle.id,
            'name': vehicle.name,
            'license_plate': vehicle.license_plate,
            'is_active': vehicle.is_active,
            'last_updated': vehicle.last_updated.isoformat() if vehicle.last_updated else None
        }

        if latest_location:
            vehicle_info.update({
                'lat': latest_location.lat,
                'lng': latest_location.lng,
                'speed': latest_location.speed,
                'timestamp': latest_location.timestamp.isoformat()
            })

        vehicle_data.append(vehicle_info)

    return jsonify(vehicle_data)

@tracking_bp.route('/api/vehicles/<int:vehicle_id>/details')
@login_required
def get_vehicle_details(vehicle_id):
    """Get detailed information about a specific vehicle"""
    vehicle = Vehicle.query.get_or_404(vehicle_id)

    # Check permissions - customers can only see their assigned vehicles
    if current_user.role == 'customer':
        # TODO: Implement vehicle assignment logic
        pass

    # Get latest location
    latest_location = Location.query.filter_by(vehicle_id=vehicle.id)\
        .order_by(Location.timestamp.desc()).first()

    vehicle_data = {
        'id': vehicle.id,
        'name': vehicle.name,
        'license_plate': vehicle.license_plate,
        'is_active': vehicle.is_active,
        'last_updated': vehicle.last_updated.isoformat() if vehicle.last_updated else None
    }

    if latest_location:
        vehicle_data.update({
            'lat': latest_location.lat,
            'lng': latest_location.lng,
            'speed': latest_location.speed,
            'timestamp': latest_location.timestamp.isoformat()
        })

    return jsonify(vehicle_data)

@tracking_bp.route('/api/vehicles/<int:vehicle_id>/history')
@login_required
def get_vehicle_history(vehicle_id):
    """Get location history for a specific vehicle"""
    vehicle = Vehicle.query.get_or_404(vehicle_id)

    # Check permissions
    if current_user.role == 'customer':
        # TODO: Implement vehicle assignment logic
        pass

    # Parse date/time filters
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    time_from = request.args.get('time_from')
    time_to = request.args.get('time_to')

    # Build query
    query = Location.query.filter_by(vehicle_id=vehicle_id)

    if date_from:
        start_datetime = datetime.strptime(date_from, '%Y-%m-%d')
        if time_from:
            time_obj = datetime.strptime(time_from, '%H:%M').time()
            start_datetime = datetime.combine(start_datetime.date(), time_obj)
        query = query.filter(Location.timestamp >= start_datetime)

    if date_to:
        end_datetime = datetime.strptime(date_to, '%Y-%m-%d')
        if time_to:
            time_obj = datetime.strptime(time_to, '%H:%M').time()
            end_datetime = datetime.combine(end_datetime.date(), time_obj)
        else:
            end_datetime = end_datetime.replace(hour=23, minute=59, second=59)
        query = query.filter(Location.timestamp <= end_datetime)

    locations = query.order_by(Location.timestamp.asc()).all()

    # Calculate summary statistics
    summary = calculate_trip_summary(locations)

    location_data = []
    for location in locations:
        location_data.append({
            'lat': location.lat,
            'lng': location.lng,
            'speed': location.speed,
            'timestamp': location.timestamp.isoformat()
        })

    return jsonify({
        'locations': location_data,
        'summary': summary
    })

@tracking_bp.route('/api/vehicles/<int:vehicle_id>/export')
@login_required
def export_vehicle_data(vehicle_id):
    """Export vehicle location data"""
    vehicle = Vehicle.query.get_or_404(vehicle_id)
    export_format = request.args.get('format', 'csv')

    # Get filtered locations (reuse history logic)
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')

    query = Location.query.filter_by(vehicle_id=vehicle_id)

    if date_from:
        start_datetime = datetime.strptime(date_from, '%Y-%m-%d')
        query = query.filter(Location.timestamp >= start_datetime)

    if date_to:
        end_datetime = datetime.strptime(date_to, '%Y-%m-%d').replace(hour=23, minute=59, second=59)
        query = query.filter(Location.timestamp <= end_datetime)

    locations = query.order_by(Location.timestamp.asc()).all()

    if export_format == 'csv':
        output = io.StringIO()
        writer = csv.writer(output)

        # Write header
        writer.writerow(['Timestamp', 'Latitude', 'Longitude', 'Speed (km/h)', 'Vehicle', 'License Plate'])

        # Write data
        for location in locations:
            writer.writerow([
                location.timestamp.isoformat(),
                location.lat,
                location.lng,
                location.speed or '',
                vehicle.name,
                vehicle.license_plate
            ])

        output.seek(0)
        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={'Content-Disposition': f'attachment; filename={vehicle.license_plate}_locations.csv'}
        )

    elif export_format == 'json':
        data = {
            'vehicle': {
                'id': vehicle.id,
                'name': vehicle.name,
                'license_plate': vehicle.license_plate
            },
            'locations': [
                {
                    'timestamp': location.timestamp.isoformat(),
                    'lat': location.lat,
                    'lng': location.lng,
                    'speed': location.speed
                }
                for location in locations
            ]
        }

        return Response(
            json.dumps(data, indent=2),
            mimetype='application/json',
            headers={'Content-Disposition': f'attachment; filename={vehicle.license_plate}_locations.json'}
        )

    return jsonify({'error': 'Unsupported format'}), 400

def calculate_trip_summary(locations):
    """Calculate trip summary statistics"""
    if not locations:
        return {}

    total_distance = 0
    speeds = [loc.speed for loc in locations if loc.speed]

    # Calculate distance between consecutive points
    for i in range(1, len(locations)):
        prev_loc = locations[i-1]
        curr_loc = locations[i]

        # Simple distance calculation (should use proper geospatial calculation)
        lat_diff = curr_loc.lat - prev_loc.lat
        lng_diff = curr_loc.lng - prev_loc.lng
        distance = ((lat_diff ** 2) + (lng_diff ** 2)) ** 0.5 * 111  # Rough km conversion
        total_distance += distance

    # Calculate time duration
    start_time = locations[0].timestamp
    end_time = locations[-1].timestamp
    duration = end_time - start_time

    return {
        'total_distance': f"{total_distance:.2f} km",
        'total_time': str(duration),
        'avg_speed': f"{sum(speeds) / len(speeds):.1f} km/h" if speeds else "N/A",
        'max_speed': f"{max(speeds):.1f} km/h" if speeds else "N/A",
        'data_points': len(locations)
    }