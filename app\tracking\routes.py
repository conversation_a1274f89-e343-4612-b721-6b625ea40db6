from flask import Blueprint, request, jsonify, render_template
from flask_login import login_required
from datetime import datetime
from app.extensions import db
from .models import Vehicle, Location, Geofence
from .services import check_geofence
from app.auth.decorators import admin_required

tracking_bp = Blueprint('tracking', __name__)

# --- Device Communication ---
@tracking_bp.route('/api/gps', methods=['POST'])
def handle_gps_update():
    data = request.json
    vehicle = Vehicle.query.filter_by(license_plate=data['plate']).first()
    
    if not vehicle:
        return jsonify({"error": "Vehicle not found"}), 404

    # Save location
    location = Location(
        vehicle_id=vehicle.id,
        lat=data['lat'],
        lng=data['lng'],
        speed=data.get('speed')
    )
    db.session.add(location)
    
    # Update vehicle last seen
    vehicle.last_updated = datetime.utcnow()
    db.session.commit()
    
    # Check geofence
    check_geofence(data['lat'], data['lng'], vehicle.id)
    return jsonify({"status": "success"})

# --- Web Interface ---
@tracking_bp.route('/map')
@login_required
@admin_required
def live_map():
    vehicles = Vehicle.query.filter_by(is_active=True).all()
    return render_template('tracking/live_map.html', vehicles=vehicles)

@tracking_bp.route('/history/<int:vehicle_id>')
@login_required
def route_history(vehicle_id):
    locations = Location.query.filter_by(vehicle_id=vehicle_id).all()
    return render_template('tracking/history.html', locations=locations)