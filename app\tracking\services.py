from shapely.geometry import Point, Polygon
from app.tracking.models import Geofence, Vehicle
from app.auth.models import User
from flask import current_app
import logging

logger = logging.getLogger(__name__)

def check_geofence(lat, lng, vehicle_id):
    """
    Check if vehicle location violates any geofences and trigger alerts.

    Args:
        lat: Latitude
        lng: Longitude
        vehicle_id: ID of the vehicle
    """
    try:
        point = Point(lng, lat)
        geofences = Geofence.query.filter_by(is_active=True).all()

        for fence in geofences:
            try:
                # Create polygon from coordinates
                if isinstance(fence.coordinates, list) and len(fence.coordinates) > 0:
                    # Handle different coordinate formats
                    if isinstance(fence.coordinates[0], list):
                        # Polygon format: [[lng, lat], [lng, lat], ...]
                        coords = fence.coordinates
                    else:
                        # GeoJSON format: {"type": "Polygon", "coordinates": [[[lng, lat], ...]]}
                        coords = fence.coordinates.get('coordinates', [[]])[0]

                    polygon = Polygon(coords)

                    # Check fence type and trigger appropriate alerts
                    if fence.fence_type == 'inclusion':
                        # Alert when vehicle exits inclusion zone
                        if not polygon.contains(point) and fence.alert_on_exit:
                            trigger_geofence_alert(vehicle_id, fence.name, 'exit')
                    elif fence.fence_type == 'exclusion':
                        # Alert when vehicle enters exclusion zone
                        if polygon.contains(point) and fence.alert_on_enter:
                            trigger_geofence_alert(vehicle_id, fence.name, 'enter')

            except Exception as e:
                logger.error(f"Error processing geofence {fence.name}: {str(e)}")
                continue

    except Exception as e:
        logger.error(f"Error in geofence check: {str(e)}")

def trigger_geofence_alert(vehicle_id, geofence_name, alert_type):
    """
    Trigger geofence alert using Celery task.

    Args:
        vehicle_id: ID of the vehicle
        geofence_name: Name of the geofence
        alert_type: Type of alert ('enter' or 'exit')
    """
    try:
        # Import here to avoid circular imports
        from app.tasks.notifications import send_geofence_alert

        # Trigger async alert
        result = send_geofence_alert.delay(vehicle_id, geofence_name, alert_type)
        logger.info(f"Geofence alert triggered for vehicle {vehicle_id}: {alert_type} {geofence_name}")

        return result

    except Exception as e:
        logger.error(f"Failed to trigger geofence alert: {str(e)}")
        # Fallback to synchronous alert
        trigger_alert_sync(vehicle_id, f"Geofence {alert_type}: {geofence_name}")

def trigger_speed_alert(vehicle_id, current_speed, speed_limit):
    """
    Trigger speed violation alert using Celery task.

    Args:
        vehicle_id: ID of the vehicle
        current_speed: Current speed of the vehicle
        speed_limit: Speed limit that was exceeded
    """
    try:
        from app.tasks.notifications import send_speed_alert

        result = send_speed_alert.delay(vehicle_id, current_speed, speed_limit)
        logger.info(f"Speed alert triggered for vehicle {vehicle_id}: {current_speed} km/h (limit: {speed_limit} km/h)")

        return result

    except Exception as e:
        logger.error(f"Failed to trigger speed alert: {str(e)}")
        # Fallback to synchronous alert
        trigger_alert_sync(vehicle_id, f"Speed violation: {current_speed} km/h (limit: {speed_limit} km/h)")

def trigger_alert_sync(vehicle_id, message):
    """
    Synchronous fallback alert function.

    Args:
        vehicle_id: ID of the vehicle
        message: Alert message
    """
    try:
        # Get vehicle info
        vehicle = Vehicle.query.get(vehicle_id)
        if not vehicle:
            logger.error(f"Vehicle {vehicle_id} not found for alert")
            return

        # Log alert
        current_app.logger.warning(f"ALERT - Vehicle {vehicle.license_plate}: {message}")

        # Send to admins (basic implementation)
        admins = User.query.filter(
            User.role.in_(['admin', 'superadmin']),
            User.is_active == True
        ).all()

        for admin in admins:
            current_app.logger.info(f"Alert notification for {admin.email}: {message}")

    except Exception as e:
        logger.error(f"Failed to send synchronous alert: {str(e)}")

def process_location_batch(location_data_list):
    """
    Process a batch of location data using Celery.

    Args:
        location_data_list: List of location data dictionaries
    """
    try:
        from app.tasks.data_processing import process_bulk_location_data

        result = process_bulk_location_data.delay(location_data_list)
        logger.info(f"Bulk location processing initiated for {len(location_data_list)} locations")

        return result

    except Exception as e:
        logger.error(f"Failed to initiate bulk location processing: {str(e)}")
        return None

def schedule_vehicle_report(vehicle_id, start_date, end_date, report_type='custom'):
    """
    Schedule vehicle report generation.

    Args:
        vehicle_id: ID of the vehicle
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
        report_type: Type of report
    """
    try:
        from app.tasks.reports import generate_vehicle_report

        result = generate_vehicle_report.delay(vehicle_id, start_date, end_date, report_type)
        logger.info(f"Vehicle report scheduled for vehicle {vehicle_id}")

        return result

    except Exception as e:
        logger.error(f"Failed to schedule vehicle report: {str(e)}")
        return None

def schedule_data_export(vehicle_id, start_date, end_date, export_format='csv'):
    """
    Schedule data export task.

    Args:
        vehicle_id: ID of the vehicle
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
        export_format: Export format ('csv', 'json')
    """
    try:
        if export_format == 'csv':
            from app.tasks.reports import export_vehicle_data_csv
            result = export_vehicle_data_csv.delay(vehicle_id, start_date, end_date)
        else:
            # Add other export formats as needed
            logger.warning(f"Export format {export_format} not implemented")
            return None

        logger.info(f"Data export scheduled for vehicle {vehicle_id}")
        return result

    except Exception as e:
        logger.error(f"Failed to schedule data export: {str(e)}")
        return None