from flask import Flask
from app.config import config
from app.extensions import init_extensions

def create_app(config_name='default'):
    """Application factory function."""
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config[config_name])

    # Initialize extensions
    init_extensions(app)

    # Register blueprints
    from app.auth.routes import auth_bp
    from app.tracking.routes import tracking_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(tracking_bp, url_prefix='/tracking')

    # Create dashboard blueprint (referenced in auth routes)
    from flask import Blueprint, render_template
    from flask_login import login_required

    dashboard_bp = Blueprint('dashboard', __name__)

    @dashboard_bp.route('/')
    @login_required
    def index():
        return render_template('dashboard/index.html')

    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')

    # Add a simple root route
    @app.route('/')
    def home():
        return '<h1>Ventsys Fleet Tracking</h1><p><a href="/auth/login">Login</a></p>'

    # Register error handlers
    register_error_handlers(app)

    # Initialize Celery
    init_celery(app)

    # Initialize logging
    init_logging(app)

    return app

def register_error_handlers(app):
    """Register error handlers for the application."""
    from flask import render_template
    import uuid

    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

    @app.errorhandler(500)
    def internal_error(error):
        from app.extensions import db
        db.session.rollback()
        error_id = str(uuid.uuid4())[:8]
        app.logger.error(f'Server Error {error_id}: {error}')
        return render_template('errors/500.html', error_id=error_id), 500

def init_celery(app):
    """Initialize Celery with Flask app context."""
    try:
        from celery_app import celery_app

        # Update Celery configuration with Flask config
        celery_app.conf.update(
            broker_url=app.config.get('CELERY_BROKER_URL'),
            result_backend=app.config.get('CELERY_RESULT_BACKEND'),
            task_always_eager=app.config.get('CELERY_TASK_ALWAYS_EAGER', False)
        )

        # Create task context
        class ContextTask(celery_app.Task):
            """Make celery tasks work with Flask app context."""
            def __call__(self, *args, **kwargs):
                with app.app_context():
                    return self.run(*args, **kwargs)

        celery_app.Task = ContextTask
        app.celery = celery_app

        app.logger.info("Celery initialized successfully")

    except ImportError:
        app.logger.warning("Celery not available - async tasks will be disabled")
    except Exception as e:
        app.logger.error(f"Failed to initialize Celery: {str(e)}")

def init_logging(app):
    """Initialize comprehensive logging system."""
    try:
        from app.utils.logging_config import setup_logging, setup_request_logging

        # Set up logging configuration
        setup_logging(app)

        # Set up request/response logging
        setup_request_logging(app)

        app.logger.info("Logging system initialized successfully")

    except Exception as e:
        # Fallback to basic logging if setup fails
        import logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        app.logger.error(f"Failed to initialize advanced logging: {str(e)}")
        app.logger.info("Using basic logging configuration")