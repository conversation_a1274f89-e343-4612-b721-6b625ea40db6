from flask import Flask
from app.config import config
from app.extensions import init_extensions

def create_app(config_name='default'):
    """Application factory function."""
    app = Flask(__name__)

    # Load configuration
    app.config.from_object(config[config_name])

    # Initialize extensions
    init_extensions(app)

    # Register blueprints
    from app.auth.routes import auth_bp
    from app.tracking.routes import tracking_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(tracking_bp, url_prefix='/tracking')

    # Create dashboard blueprint (referenced in auth routes)
    from flask import Blueprint, render_template
    from flask_login import login_required

    dashboard_bp = Blueprint('dashboard', __name__)

    @dashboard_bp.route('/')
    @login_required
    def index():
        return render_template('dashboard/index.html')

    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')

    # Add a simple root route
    @app.route('/')
    def home():
        return '<h1>Ventsys Fleet Tracking</h1><p><a href="/auth/login">Login</a></p>'

    # Register error handlers
    register_error_handlers(app)

    return app

def register_error_handlers(app):
    """Register error handlers for the application."""
    from flask import render_template
    import uuid

    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

    @app.errorhandler(500)
    def internal_error(error):
        from app.extensions import db
        db.session.rollback()
        error_id = str(uuid.uuid4())[:8]
        app.logger.error(f'Server Error {error_id}: {error}')
        return render_template('errors/500.html', error_id=error_id), 500