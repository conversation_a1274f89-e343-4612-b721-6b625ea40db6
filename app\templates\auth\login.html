{% extends 'base.html' %}

{% block title %}Login - Ventsys{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="text-center mb-0">
                    <i class="bi bi-shield-lock me-2"></i>Login to Ventsys
                </h3>
            </div>
            <div class="card-body p-4">
                <form method="POST" id="loginForm" novalidate>
                    {{ csrf_token() }}

                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="bi bi-envelope me-1"></i>Email Address
                        </label>
                        <input type="email"
                               class="form-control"
                               id="email"
                               name="email"
                               required
                               autocomplete="email"
                               placeholder="Enter your email address">
                        <div class="invalid-feedback">
                            Please provide a valid email address.
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="bi bi-key me-1"></i>Password
                        </label>
                        <div class="input-group">
                            <input type="password"
                                   class="form-control"
                                   id="password"
                                   name="password"
                                   required
                                   autocomplete="current-password"
                                   placeholder="Enter your password">
                            <button class="btn btn-outline-secondary"
                                    type="button"
                                    id="togglePassword"
                                    title="Show/Hide Password">
                                <i class="bi bi-eye" id="togglePasswordIcon"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            Password is required.
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                        <label class="form-check-label" for="rememberMe">
                            Remember me for 30 days
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary w-100 mb-3" id="loginBtn">
                        <span class="spinner-border spinner-border-sm me-2 d-none" id="loginSpinner"></span>
                        <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                    </button>
                </form>

                <div class="text-center">
                    <a href="{{ url_for('auth.forgot_password') }}" class="text-decoration-none">
                        <i class="bi bi-question-circle me-1"></i>Forgot your password?
                    </a>
                </div>
            </div>
            <div class="card-footer text-center text-muted">
                <small>
                    <i class="bi bi-shield-check me-1"></i>
                    Secure login protected by encryption
                </small>
            </div>
        </div>

        <!-- Demo Credentials (Development Only) -->
        {% if config.DEBUG %}
        <div class="card mt-3 border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0"><i class="bi bi-info-circle me-1"></i>Demo Credentials</h6>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <strong>Superadmin:</strong> <EMAIL> / admin123<br>
                    <strong>Admin:</strong> <EMAIL> / manager123<br>
                    <strong>Customer:</strong> <EMAIL> / customer123
                </small>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const loginBtn = document.getElementById('loginBtn');
    const loginSpinner = document.getElementById('loginSpinner');
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const togglePasswordIcon = document.getElementById('togglePasswordIcon');

    // Form validation
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!loginForm.checkValidity()) {
            e.stopPropagation();
            loginForm.classList.add('was-validated');
            return;
        }

        // Show loading state
        loginBtn.disabled = true;
        loginSpinner.classList.remove('d-none');

        // Submit form
        const formData = new FormData(loginForm);

        fetch(loginForm.action || window.location.pathname, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => {
            if (response.redirected) {
                window.location.href = response.url;
            } else {
                return response.text();
            }
        })
        .then(html => {
            if (html) {
                // If we get HTML back, there was an error
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const alerts = doc.querySelectorAll('.alert');

                // Show error messages
                alerts.forEach(alert => {
                    showAlert(alert.textContent.trim(), 'danger');
                });

                // Reset form state
                loginBtn.disabled = false;
                loginSpinner.classList.add('d-none');
            }
        })
        .catch(error => {
            console.error('Login error:', error);
            showAlert('An error occurred during login. Please try again.', 'danger');
            loginBtn.disabled = false;
            loginSpinner.classList.add('d-none');
        });
    });

    // Toggle password visibility
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        if (type === 'password') {
            togglePasswordIcon.className = 'bi bi-eye';
        } else {
            togglePasswordIcon.className = 'bi bi-eye-slash';
        }
    });

    // Real-time validation
    const inputs = loginForm.querySelectorAll('input[required]');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });

        input.addEventListener('input', function() {
            if (this.classList.contains('is-invalid') && this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            }
        });
    });
});
</script>
{% endblock %}