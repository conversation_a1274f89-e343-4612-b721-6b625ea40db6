{% extends 'base.html' %}

{% block title %}Forgot Password - Ventsys{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-5">
        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <h3 class="text-center mb-0">
                    <i class="bi bi-key me-2"></i>Reset Password
                </h3>
            </div>
            <div class="card-body p-4">
                <p class="text-muted text-center mb-4">
                    Enter your email address and we'll send you a link to reset your password.
                </p>
                
                <form method="POST" id="forgotPasswordForm" novalidate>
                    {{ csrf_token() }}
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="bi bi-envelope me-1"></i>Email Address
                        </label>
                        <input type="email" 
                               class="form-control" 
                               id="email" 
                               name="email" 
                               required 
                               autocomplete="email"
                               placeholder="Enter your email address">
                        <div class="invalid-feedback">
                            Please provide a valid email address.
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-warning w-100 mb-3" id="resetBtn">
                        <span class="spinner-border spinner-border-sm me-2 d-none" id="resetSpinner"></span>
                        <i class="bi bi-envelope-arrow-up me-2"></i>Send Reset Link
                    </button>
                </form>
                
                <div class="text-center">
                    <a href="{{ url_for('auth.login') }}" class="text-decoration-none">
                        <i class="bi bi-arrow-left me-1"></i>Back to Login
                    </a>
                </div>
            </div>
            <div class="card-footer text-center text-muted">
                <small>
                    <i class="bi bi-info-circle me-1"></i>
                    If you don't receive an email, check your spam folder or contact support.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('forgotPasswordForm');
    const resetBtn = document.getElementById('resetBtn');
    const resetSpinner = document.getElementById('resetSpinner');
    const emailInput = document.getElementById('email');
    
    // Form validation and submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!form.checkValidity()) {
            e.stopPropagation();
            form.classList.add('was-validated');
            return;
        }
        
        // Show loading state
        resetBtn.disabled = true;
        resetSpinner.classList.remove('d-none');
        
        // Simulate API call (replace with actual implementation)
        setTimeout(() => {
            showAlert('If an account with that email exists, we\'ve sent a password reset link.', 'success');
            resetBtn.disabled = false;
            resetSpinner.classList.add('d-none');
            form.reset();
            form.classList.remove('was-validated');
        }, 2000);
    });
    
    // Real-time validation
    emailInput.addEventListener('blur', function() {
        if (this.checkValidity()) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        } else {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
        }
    });
    
    emailInput.addEventListener('input', function() {
        if (this.classList.contains('is-invalid') && this.checkValidity()) {
            this.classList.remove('is-invalid');
            this.classList.add('is-valid');
        }
    });
});
</script>
{% endblock %}
