from app.extensions import db
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

class User(db.Model, UserMixin):
    id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False, index=True)  # 'superadmin', 'admin', 'customer'
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True, nullable=False)

    # Profile fields
    first_name = db.Column(db.String(50))
    last_name = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    timezone = db.Column(db.String(50), default='UTC')

    # Notification preferences
    email_notifications = db.Column(db.<PERSON><PERSON>, default=True)
    sms_notifications = db.Column(db.<PERSON>, default=False)
    weekly_reports = db.Column(db.<PERSON>, default=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_login = db.Column(db.DateTime)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def set_password(self, password):
        """Hash and set the user's password."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check if the provided password matches the stored hash."""
        return check_password_hash(self.password_hash, password)

    @property
    def full_name(self):
        """Get user's full name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        else:
            return self.email.split('@')[0]

    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary."""
        data = {
            'id': self.id,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'full_name': self.full_name,
            'phone': self.phone,
            'timezone': self.timezone,
            'email_notifications': self.email_notifications,
            'sms_notifications': self.sms_notifications,
            'weekly_reports': self.weekly_reports,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None
        }

        if include_sensitive:
            # Only include sensitive data when explicitly requested
            pass

        return data

    def __repr__(self):
        return f'<User {self.email} ({self.role})>'