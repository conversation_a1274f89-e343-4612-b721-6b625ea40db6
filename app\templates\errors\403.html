{% extends 'base.html' %}

{% block title %}Access Forbidden - Ventsys{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6 text-center">
        <div class="card shadow">
            <div class="card-body p-5">
                <div class="mb-4">
                    <i class="bi bi-shield-x text-danger" style="font-size: 5rem;"></i>
                </div>
                
                <h1 class="display-4 text-muted">403</h1>
                <h2 class="mb-3">Access Forbidden</h2>
                
                <p class="lead text-muted mb-4">
                    You don't have permission to access this resource.
                </p>
                
                {% if current_user.is_authenticated %}
                <div class="alert alert-warning" role="alert">
                    <i class="bi bi-person-badge me-2"></i>
                    <strong>Current Role:</strong> {{ current_user.role.title() }}<br>
                    <small class="text-muted">This page requires different permissions.</small>
                </div>
                {% else %}
                <div class="alert alert-info" role="alert">
                    <i class="bi bi-info-circle me-2"></i>
                    You need to log in to access this page.
                </div>
                {% endif %}
                
                <div class="d-grid gap-2 d-md-block">
                    {% if current_user.is_authenticated %}
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                        <i class="bi bi-speedometer2 me-2"></i>Go to Dashboard
                    </a>
                    {% else %}
                    <a href="{{ url_for('auth.login') }}" class="btn btn-primary">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Login
                    </a>
                    {% endif %}
                    <button class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="bi bi-arrow-left me-2"></i>Go Back
                    </button>
                </div>
                
                <hr class="my-4">
                
                <div class="text-muted">
                    <p class="mb-2">Need access? Contact your administrator:</p>
                    <p>
                        <i class="bi bi-envelope me-1"></i>
                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
        
        {% if current_user.is_authenticated %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Role Permissions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success">Superadmin</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check-circle text-success me-1"></i>Full system access</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>User management</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>All fleet features</li>
                        </ul>
                        
                        <h6 class="text-primary">Admin</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check-circle text-success me-1"></i>Fleet management</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>Live tracking</li>
                            <li><i class="bi bi-x-circle text-danger me-1"></i>User creation</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">Customer</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check-circle text-success me-1"></i>Assigned vehicles</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>Basic tracking</li>
                            <li><i class="bi bi-x-circle text-danger me-1"></i>System management</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-redirect to appropriate page after 15 seconds
setTimeout(function() {
    {% if current_user.is_authenticated %}
    window.location.href = "{{ url_for('dashboard.index') }}";
    {% else %}
    window.location.href = "{{ url_for('auth.login') }}";
    {% endif %}
}, 15000);
</script>
{% endblock %}
