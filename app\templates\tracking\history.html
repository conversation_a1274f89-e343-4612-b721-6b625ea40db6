{% extends 'base.html' %}

{% block title %}Vehicle History - Ventsys{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1>Vehicle History</h1>
                {% if vehicle %}
                <p class="text-muted">{{ vehicle.name }} ({{ vehicle.license_plate }})</p>
                {% endif %}
            </div>
            <div>
                <a href="{{ url_for('tracking.live_map') }}" class="btn btn-secondary">Back to Live Map</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5>Filter Options</h5>
            </div>
            <div class="card-body">
                <form id="filterForm">
                    <div class="mb-3">
                        <label for="dateFrom" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="dateFrom" name="date_from">
                    </div>
                    <div class="mb-3">
                        <label for="dateTo" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="dateTo" name="date_to">
                    </div>
                    <div class="mb-3">
                        <label for="timeFrom" class="form-label">From Time</label>
                        <input type="time" class="form-control" id="timeFrom" name="time_from">
                    </div>
                    <div class="mb-3">
                        <label for="timeTo" class="form-label">To Time</label>
                        <input type="time" class="form-control" id="timeTo" name="time_to">
                    </div>
                    <button type="submit" class="btn btn-primary w-100">Apply Filter</button>
                    <button type="button" class="btn btn-secondary w-100 mt-2" onclick="clearFilters()">Clear</button>
                </form>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5>Trip Summary</h5>
            </div>
            <div class="card-body">
                <div id="tripSummary">
                    <p><strong>Total Distance:</strong> <span id="totalDistance">-</span></p>
                    <p><strong>Total Time:</strong> <span id="totalTime">-</span></p>
                    <p><strong>Average Speed:</strong> <span id="avgSpeed">-</span></p>
                    <p><strong>Max Speed:</strong> <span id="maxSpeed">-</span></p>
                    <p><strong>Data Points:</strong> <span id="dataPoints">-</span></p>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5>Export Options</h5>
            </div>
            <div class="card-body">
                <button class="btn btn-success btn-sm w-100 mb-2" onclick="exportData('csv')">Export CSV</button>
                <button class="btn btn-info btn-sm w-100 mb-2" onclick="exportData('json')">Export JSON</button>
                <button class="btn btn-warning btn-sm w-100" onclick="generateReport()">Generate Report</button>
            </div>
        </div>
    </div>

    <div class="col-md-9">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5>Route History</h5>
                    <div>
                        <div class="form-check form-switch d-inline-block me-3">
                            <input class="form-check-input" type="checkbox" id="showMarkers" checked>
                            <label class="form-check-label" for="showMarkers">Show Markers</label>
                        </div>
                        <div class="form-check form-switch d-inline-block">
                            <input class="form-check-input" type="checkbox" id="animateRoute">
                            <label class="form-check-label" for="animateRoute">Animate Route</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="historyMap" style="height: 500px; width: 100%;"></div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5>Location Data</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="locationTable">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Latitude</th>
                                <th>Longitude</th>
                                <th>Speed (km/h)</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="locationTableBody">
                            <!-- Data will be loaded here -->
                        </tbody>
                    </table>
                </div>
                <nav>
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- Pagination will be generated here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Location Details Modal -->
<div class="modal fade" id="locationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Location Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="locationModalBody">
                <!-- Location details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Leaflet CSS and JS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let historyMap;
let routeLayer;
let markersLayer;
let currentLocations = [];
let currentPage = 1;
const itemsPerPage = 20;

// Get vehicle ID from URL
const vehicleId = {{ vehicle.id if vehicle else 'null' }};

// Initialize map and load data
document.addEventListener('DOMContentLoaded', function() {
    initHistoryMap();
    setDefaultDates();
    loadLocationHistory();

    // Set up form submission
    document.getElementById('filterForm').addEventListener('submit', function(e) {
        e.preventDefault();
        loadLocationHistory();
    });
});

// Initialize history map
function initHistoryMap() {
    historyMap = L.map('historyMap').setView([40.7128, -74.0060], 10);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(historyMap);

    // Initialize layers
    routeLayer = L.layerGroup().addTo(historyMap);
    markersLayer = L.layerGroup().addTo(historyMap);
}

// Set default date range (last 7 days)
function setDefaultDates() {
    const today = new Date();
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    document.getElementById('dateTo').value = today.toISOString().split('T')[0];
    document.getElementById('dateFrom').value = weekAgo.toISOString().split('T')[0];
}

// Load location history data
function loadLocationHistory() {
    if (!vehicleId) {
        showAlert('No vehicle selected', 'warning');
        return;
    }

    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);

    fetch(`/tracking/api/vehicles/${vehicleId}/history?${params}`)
        .then(response => response.json())
        .then(data => {
            currentLocations = data.locations || [];
            updateMap(currentLocations);
            updateTable(currentLocations);
            updateSummary(data.summary || {});
        })
        .catch(error => {
            console.error('Error loading location history:', error);
            showAlert('Error loading location history', 'danger');
        });
}

// Update map with route and markers
function updateMap(locations) {
    // Clear existing layers
    routeLayer.clearLayers();
    markersLayer.clearLayers();

    if (locations.length === 0) {
        showAlert('No location data found for the selected period', 'info');
        return;
    }

    // Create route polyline
    const routePoints = locations.map(loc => [loc.lat, loc.lng]);
    const polyline = L.polyline(routePoints, {
        color: 'blue',
        weight: 3,
        opacity: 0.7
    });
    routeLayer.addLayer(polyline);

    // Add markers if enabled
    if (document.getElementById('showMarkers').checked) {
        locations.forEach((location, index) => {
            let markerColor = 'blue';
            if (index === 0) markerColor = 'green'; // Start
            if (index === locations.length - 1) markerColor = 'red'; // End

            const marker = L.circleMarker([location.lat, location.lng], {
                color: markerColor,
                radius: 5
            }).bindPopup(`
                <strong>Time:</strong> ${new Date(location.timestamp).toLocaleString()}<br>
                <strong>Speed:</strong> ${location.speed || 'N/A'} km/h<br>
                <strong>Coordinates:</strong> ${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}
            `);
            markersLayer.addLayer(marker);
        });
    }

    // Fit map to route
    historyMap.fitBounds(polyline.getBounds().pad(0.1));
}

// Update location table
function updateTable(locations) {
    const tbody = document.getElementById('locationTableBody');
    tbody.innerHTML = '';

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const pageLocations = locations.slice(startIndex, endIndex);

    pageLocations.forEach((location, index) => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${new Date(location.timestamp).toLocaleString()}</td>
            <td>${location.lat.toFixed(6)}</td>
            <td>${location.lng.toFixed(6)}</td>
            <td>${location.speed || 'N/A'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="showLocationDetails(${startIndex + index})">
                    Details
                </button>
                <button class="btn btn-sm btn-outline-secondary" onclick="centerMapOnLocation(${location.lat}, ${location.lng})">
                    Show on Map
                </button>
            </td>
        `;
    });

    updatePagination(locations.length);
}

// Update pagination
function updatePagination(totalItems) {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    if (totalPages <= 1) return;

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
    pagination.appendChild(prevLi);

    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            const li = document.createElement('li');
            li.className = `page-item ${i === currentPage ? 'active' : ''}`;
            li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
            pagination.appendChild(li);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            const li = document.createElement('li');
            li.className = 'page-item disabled';
            li.innerHTML = '<span class="page-link">...</span>';
            pagination.appendChild(li);
        }
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
    pagination.appendChild(nextLi);
}

// Update trip summary
function updateSummary(summary) {
    document.getElementById('totalDistance').textContent = summary.total_distance || '-';
    document.getElementById('totalTime').textContent = summary.total_time || '-';
    document.getElementById('avgSpeed').textContent = summary.avg_speed || '-';
    document.getElementById('maxSpeed').textContent = summary.max_speed || '-';
    document.getElementById('dataPoints').textContent = summary.data_points || currentLocations.length;
}

// Utility functions
function changePage(page) {
    if (page >= 1 && page <= Math.ceil(currentLocations.length / itemsPerPage)) {
        currentPage = page;
        updateTable(currentLocations);
    }
}

function showLocationDetails(index) {
    const location = currentLocations[index];
    document.getElementById('locationModalBody').innerHTML = `
        <table class="table">
            <tr><td><strong>Timestamp:</strong></td><td>${new Date(location.timestamp).toLocaleString()}</td></tr>
            <tr><td><strong>Latitude:</strong></td><td>${location.lat.toFixed(8)}</td></tr>
            <tr><td><strong>Longitude:</strong></td><td>${location.lng.toFixed(8)}</td></tr>
            <tr><td><strong>Speed:</strong></td><td>${location.speed || 'N/A'} km/h</td></tr>
            <tr><td><strong>Accuracy:</strong></td><td>${location.accuracy || 'N/A'} m</td></tr>
        </table>
    `;
    new bootstrap.Modal(document.getElementById('locationModal')).show();
}

function centerMapOnLocation(lat, lng) {
    historyMap.setView([lat, lng], 15);
}

function clearFilters() {
    document.getElementById('filterForm').reset();
    setDefaultDates();
    loadLocationHistory();
}

function exportData(format) {
    if (currentLocations.length === 0) {
        showAlert('No data to export', 'warning');
        return;
    }

    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);
    params.append('format', format);

    window.open(`/tracking/api/vehicles/${vehicleId}/export?${params}`, '_blank');
}

function generateReport() {
    if (currentLocations.length === 0) {
        showAlert('No data to generate report', 'warning');
        return;
    }

    const formData = new FormData(document.getElementById('filterForm'));
    const params = new URLSearchParams(formData);

    window.open(`/tracking/api/vehicles/${vehicleId}/report?${params}`, '_blank');
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 3000);
}

// Event listeners for map controls
document.getElementById('showMarkers').addEventListener('change', function() {
    updateMap(currentLocations);
});
</script>
{% endblock %}