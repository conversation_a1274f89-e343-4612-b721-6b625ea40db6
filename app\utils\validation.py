"""
Comprehensive validation and sanitization utilities for the Ventsys application.
"""

import re
import html
import bleach
from urllib.parse import urlparse
from datetime import datetime, date
from typing import Any, Optional, Union, List, Dict
import json

class ValidationError(Exception):
    """Custom validation error"""
    pass

class InputSanitizer:
    """Utility class for sanitizing user inputs"""
    
    # Allowed HTML tags for rich text fields
    ALLOWED_TAGS = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'a']
    ALLOWED_ATTRIBUTES = {'a': ['href', 'title']}
    
    @staticmethod
    def sanitize_string(value: str, max_length: Optional[int] = None, 
                       allow_html: bool = False) -> str:
        """Sanitize string input"""
        if not isinstance(value, str):
            return str(value)
        
        # Remove null bytes and control characters
        value = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', value)
        
        # Strip whitespace
        value = value.strip()
        
        if allow_html:
            # Clean HTML but allow safe tags
            value = bleach.clean(
                value,
                tags=InputSanitizer.ALLOWED_TAGS,
                attributes=InputSanitizer.ALLOWED_ATTRIBUTES,
                strip=True
            )
        else:
            # Escape HTML entities
            value = html.escape(value)
        
        # Truncate if max_length specified
        if max_length and len(value) > max_length:
            value = value[:max_length]
        
        return value
    
    @staticmethod
    def sanitize_email(email: str) -> str:
        """Sanitize email address"""
        if not isinstance(email, str):
            raise ValidationError("Email must be a string")
        
        email = email.lower().strip()
        
        # Basic email validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError("Invalid email format")
        
        # Check for dangerous characters
        if any(char in email for char in ['<', '>', '"', "'"]):
            raise ValidationError("Email contains invalid characters")
        
        return email
    
    @staticmethod
    def sanitize_phone(phone: str) -> str:
        """Sanitize phone number"""
        if not phone:
            return ""
        
        # Remove all non-digit characters except + at the beginning
        phone = re.sub(r'[^\d+]', '', phone)
        
        # Ensure + is only at the beginning
        if '+' in phone:
            parts = phone.split('+')
            phone = '+' + ''.join(parts[1:])
        
        return phone
    
    @staticmethod
    def sanitize_coordinates(lat: float, lng: float) -> tuple:
        """Sanitize GPS coordinates"""
        try:
            lat = float(lat)
            lng = float(lng)
        except (ValueError, TypeError):
            raise ValidationError("Coordinates must be numeric")
        
        if not (-90 <= lat <= 90):
            raise ValidationError("Latitude must be between -90 and 90")
        
        if not (-180 <= lng <= 180):
            raise ValidationError("Longitude must be between -180 and 180")
        
        return round(lat, 8), round(lng, 8)
    
    @staticmethod
    def sanitize_json(json_str: str) -> dict:
        """Sanitize and validate JSON string"""
        if not isinstance(json_str, str):
            raise ValidationError("JSON must be a string")
        
        try:
            data = json.loads(json_str)
        except json.JSONDecodeError as e:
            raise ValidationError(f"Invalid JSON: {str(e)}")
        
        return data

class DataValidator:
    """Utility class for data validation"""
    
    @staticmethod
    def validate_license_plate(plate: str) -> str:
        """Validate and normalize license plate"""
        if not isinstance(plate, str):
            raise ValidationError("License plate must be a string")
        
        plate = plate.upper().strip()
        
        # Remove extra spaces
        plate = re.sub(r'\s+', ' ', plate)
        
        # Basic validation - alphanumeric with spaces and hyphens
        if not re.match(r'^[A-Z0-9\s\-]{2,20}$', plate):
            raise ValidationError("Invalid license plate format")
        
        return plate
    
    @staticmethod
    def validate_speed(speed: Union[int, float, None]) -> Optional[float]:
        """Validate vehicle speed"""
        if speed is None:
            return None
        
        try:
            speed = float(speed)
        except (ValueError, TypeError):
            raise ValidationError("Speed must be numeric")
        
        if speed < 0:
            raise ValidationError("Speed cannot be negative")
        
        if speed > 500:  # Reasonable maximum speed in km/h
            raise ValidationError("Speed exceeds maximum allowed value")
        
        return round(speed, 2)
    
    @staticmethod
    def validate_date_range(start_date: date, end_date: date, 
                          max_days: int = 365) -> tuple:
        """Validate date range"""
        if not isinstance(start_date, date) or not isinstance(end_date, date):
            raise ValidationError("Dates must be date objects")
        
        if start_date > end_date:
            raise ValidationError("Start date must be before end date")
        
        if (end_date - start_date).days > max_days:
            raise ValidationError(f"Date range cannot exceed {max_days} days")
        
        # Don't allow future dates
        today = date.today()
        if start_date > today or end_date > today:
            raise ValidationError("Dates cannot be in the future")
        
        return start_date, end_date
    
    @staticmethod
    def validate_vehicle_name(name: str) -> str:
        """Validate vehicle name"""
        if not isinstance(name, str):
            raise ValidationError("Vehicle name must be a string")
        
        name = name.strip()
        
        if len(name) < 2:
            raise ValidationError("Vehicle name must be at least 2 characters")
        
        if len(name) > 50:
            raise ValidationError("Vehicle name must be less than 50 characters")
        
        # Allow alphanumeric, spaces, hyphens, underscores
        if not re.match(r'^[a-zA-Z0-9\s\-_]+$', name):
            raise ValidationError("Vehicle name contains invalid characters")
        
        return name
    
    @staticmethod
    def validate_user_role(role: str) -> str:
        """Validate user role"""
        valid_roles = ['superadmin', 'admin', 'customer']
        
        if role not in valid_roles:
            raise ValidationError(f"Invalid role. Must be one of: {', '.join(valid_roles)}")
        
        return role
    
    @staticmethod
    def validate_pagination(page: int, per_page: int, max_per_page: int = 100) -> tuple:
        """Validate pagination parameters"""
        try:
            page = int(page)
            per_page = int(per_page)
        except (ValueError, TypeError):
            raise ValidationError("Page and per_page must be integers")
        
        if page < 1:
            page = 1
        
        if per_page < 1:
            per_page = 10
        elif per_page > max_per_page:
            per_page = max_per_page
        
        return page, per_page

class SecurityValidator:
    """Security-focused validation utilities"""
    
    @staticmethod
    def validate_password_strength(password: str) -> Dict[str, Any]:
        """Validate password strength and return detailed feedback"""
        if not isinstance(password, str):
            raise ValidationError("Password must be a string")
        
        feedback = {
            'is_valid': True,
            'score': 0,
            'issues': [],
            'suggestions': []
        }
        
        # Length check
        if len(password) < 8:
            feedback['is_valid'] = False
            feedback['issues'].append('Password is too short')
            feedback['suggestions'].append('Use at least 8 characters')
        else:
            feedback['score'] += 20
        
        # Character variety checks
        if not re.search(r'[a-z]', password):
            feedback['issues'].append('Missing lowercase letters')
            feedback['suggestions'].append('Add lowercase letters')
        else:
            feedback['score'] += 20
        
        if not re.search(r'[A-Z]', password):
            feedback['issues'].append('Missing uppercase letters')
            feedback['suggestions'].append('Add uppercase letters')
        else:
            feedback['score'] += 20
        
        if not re.search(r'\d', password):
            feedback['issues'].append('Missing numbers')
            feedback['suggestions'].append('Add numbers')
        else:
            feedback['score'] += 20
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            feedback['issues'].append('Missing special characters')
            feedback['suggestions'].append('Add special characters')
        else:
            feedback['score'] += 20
        
        # Common password checks
        common_passwords = ['password', '123456', 'qwerty', 'admin', 'letmein']
        if password.lower() in common_passwords:
            feedback['is_valid'] = False
            feedback['issues'].append('Password is too common')
            feedback['suggestions'].append('Use a unique password')
            feedback['score'] = 0
        
        if feedback['score'] < 60:
            feedback['is_valid'] = False
        
        return feedback
    
    @staticmethod
    def validate_file_upload(filename: str, allowed_extensions: List[str], 
                           max_size: int = 5 * 1024 * 1024) -> str:
        """Validate file upload"""
        if not filename:
            raise ValidationError("No filename provided")
        
        # Sanitize filename
        filename = re.sub(r'[^\w\-_\.]', '', filename)
        
        # Check extension
        if '.' not in filename:
            raise ValidationError("File must have an extension")
        
        extension = filename.rsplit('.', 1)[1].lower()
        if extension not in allowed_extensions:
            raise ValidationError(f"File type not allowed. Allowed: {', '.join(allowed_extensions)}")
        
        return filename
    
    @staticmethod
    def validate_url(url: str, allowed_schemes: List[str] = ['http', 'https']) -> str:
        """Validate URL"""
        if not isinstance(url, str):
            raise ValidationError("URL must be a string")
        
        try:
            parsed = urlparse(url)
        except Exception:
            raise ValidationError("Invalid URL format")
        
        if parsed.scheme not in allowed_schemes:
            raise ValidationError(f"URL scheme not allowed. Allowed: {', '.join(allowed_schemes)}")
        
        if not parsed.netloc:
            raise ValidationError("URL must have a domain")
        
        return url
