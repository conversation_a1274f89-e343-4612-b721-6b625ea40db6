from app.extensions import db
from datetime import datetime
from sqlalchemy import Index

class Vehicle(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    license_plate = db.Column(db.String(20), unique=True, nullable=False, index=True)
    is_active = db.Column(db.Bo<PERSON>, default=True, nullable=False)
    description = db.Column(db.Text)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    locations = db.relationship('Location', backref='vehicle', lazy='dynamic',
                               cascade='all, delete-orphan')
    alerts = db.relationship('Alert', backref='vehicle', lazy='dynamic')

    def __repr__(self):
        return f'<Vehicle {self.license_plate}: {self.name}>'

    def to_dict(self):
        """Convert vehicle to dictionary."""
        latest_location = self.locations.order_by(Location.timestamp.desc()).first()

        return {
            'id': self.id,
            'name': self.name,
            'license_plate': self.license_plate,
            'is_active': self.is_active,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'latest_location': latest_location.to_dict() if latest_location else None,
            'total_locations': self.locations.count()
        }

class Location(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicle.id'), nullable=False, index=True)
    lat = db.Column(db.Float, nullable=False)
    lng = db.Column(db.Float, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)

    # Additional GPS data
    speed = db.Column(db.Float)  # km/h
    heading = db.Column(db.Float)  # degrees (0-360)
    accuracy = db.Column(db.Float)  # meters
    altitude = db.Column(db.Float)  # meters

    # Data quality indicators
    is_valid = db.Column(db.Boolean, default=True)
    source = db.Column(db.String(20), default='gps')  # 'gps', 'manual', 'estimated'

    # Create composite index for efficient queries
    __table_args__ = (
        Index('idx_vehicle_timestamp', 'vehicle_id', 'timestamp'),
        Index('idx_timestamp_vehicle', 'timestamp', 'vehicle_id'),
    )

    def __repr__(self):
        return f'<Location {self.vehicle_id} at {self.lat}, {self.lng} on {self.timestamp}>'

    def to_dict(self):
        """Convert location to dictionary."""
        return {
            'id': self.id,
            'vehicle_id': self.vehicle_id,
            'lat': self.lat,
            'lng': self.lng,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'speed': self.speed,
            'heading': self.heading,
            'accuracy': self.accuracy,
            'altitude': self.altitude,
            'is_valid': self.is_valid,
            'source': self.source
        }

class Geofence(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    coordinates = db.Column(db.JSON, nullable=False)  # GeoJSON format
    is_active = db.Column(db.Boolean, default=True, nullable=False)

    # Geofence type and behavior
    fence_type = db.Column(db.String(20), default='inclusion')  # 'inclusion', 'exclusion'
    alert_on_enter = db.Column(db.Boolean, default=True)
    alert_on_exit = db.Column(db.Boolean, default=True)

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Relationships
    alerts = db.relationship('Alert', backref='geofence', lazy='dynamic')

    def __repr__(self):
        return f'<Geofence {self.name}>'

    def to_dict(self):
        """Convert geofence to dictionary."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'coordinates': self.coordinates,
            'is_active': self.is_active,
            'fence_type': self.fence_type,
            'alert_on_enter': self.alert_on_enter,
            'alert_on_exit': self.alert_on_exit,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'created_by': self.created_by
        }

class Alert(db.Model):
    """Model for storing alerts and notifications"""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    message = db.Column(db.Text, nullable=False)
    alert_type = db.Column(db.String(20), nullable=False, index=True)  # 'info', 'warning', 'critical', etc.

    # Related entities
    vehicle_id = db.Column(db.Integer, db.ForeignKey('vehicle.id'), index=True)
    geofence_id = db.Column(db.Integer, db.ForeignKey('geofence.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))  # User who should receive the alert

    # Alert status
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    is_read = db.Column(db.Boolean, default=False, nullable=False)
    acknowledged_at = db.Column(db.DateTime)
    acknowledged_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False, index=True)
    expires_at = db.Column(db.DateTime)

    # Notification tracking
    email_sent = db.Column(db.Boolean, default=False)
    sms_sent = db.Column(db.Boolean, default=False)
    push_sent = db.Column(db.Boolean, default=False)

    def __repr__(self):
        return f'<Alert {self.alert_type}: {self.title}>'

    def to_dict(self):
        """Convert alert to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'alert_type': self.alert_type,
            'vehicle_id': self.vehicle_id,
            'geofence_id': self.geofence_id,
            'user_id': self.user_id,
            'is_active': self.is_active,
            'is_read': self.is_read,
            'acknowledged_at': self.acknowledged_at.isoformat() if self.acknowledged_at else None,
            'acknowledged_by': self.acknowledged_by,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'email_sent': self.email_sent,
            'sms_sent': self.sms_sent,
            'push_sent': self.push_sent
        }