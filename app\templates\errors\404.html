{% extends 'base.html' %}

{% block title %}Page Not Found - Ventsys{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6 text-center">
        <div class="card shadow">
            <div class="card-body p-5">
                <div class="mb-4">
                    <i class="bi bi-exclamation-triangle text-warning" style="font-size: 5rem;"></i>
                </div>
                
                <h1 class="display-4 text-muted">404</h1>
                <h2 class="mb-3">Page Not Found</h2>
                
                <p class="lead text-muted mb-4">
                    Sorry, the page you are looking for doesn't exist or has been moved.
                </p>
                
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('dashboard.index') if current_user.is_authenticated else url_for('home') }}" 
                       class="btn btn-primary">
                        <i class="bi bi-house me-2"></i>Go Home
                    </a>
                    <button class="btn btn-outline-secondary" onclick="history.back()">
                        <i class="bi bi-arrow-left me-2"></i>Go Back
                    </button>
                </div>
                
                <hr class="my-4">
                
                <div class="text-muted">
                    <p class="mb-2">If you think this is an error, please contact support:</p>
                    <p>
                        <i class="bi bi-envelope me-1"></i>
                        <a href="mailto:<EMAIL>" class="text-decoration-none"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
        
        {% if current_user.is_authenticated %}
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-compass me-2"></i>Quick Navigation</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><a href="{{ url_for('dashboard.index') }}" class="text-decoration-none">
                                <i class="bi bi-speedometer2 me-1"></i>Dashboard
                            </a></li>
                            {% if current_user.role in ['admin', 'superadmin'] %}
                            <li><a href="{{ url_for('tracking.live_map') }}" class="text-decoration-none">
                                <i class="bi bi-geo-alt me-1"></i>Live Map
                            </a></li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            {% if current_user.role == 'superadmin' %}
                            <li><a href="{{ url_for('auth.create_user') }}" class="text-decoration-none">
                                <i class="bi bi-person-plus me-1"></i>Create User
                            </a></li>
                            {% endif %}
                            <li><a href="{{ url_for('auth.profile') }}" class="text-decoration-none">
                                <i class="bi bi-person-circle me-1"></i>Profile
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-redirect after 30 seconds if user is not authenticated
{% if not current_user.is_authenticated %}
setTimeout(function() {
    window.location.href = "{{ url_for('home') }}";
}, 30000);
{% endif %}
</script>
{% endblock %}
