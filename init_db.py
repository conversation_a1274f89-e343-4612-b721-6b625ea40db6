#!/usr/bin/env python3
"""
Database initialization script for Ventsys Flask application.
This script creates the database tables and an initial superadmin user.
"""

import os
from app import create_app
from app.extensions import db
from app.auth.models import User

def init_database():
    """Initialize the database and create tables."""
    app = create_app('development')
    
    with app.app_context():
        # Create all database tables
        db.create_all()
        print("Database tables created successfully!")
        
        # Check if superadmin user already exists
        existing_superadmin = User.query.filter_by(role='superadmin').first()
        if existing_superadmin:
            print(f"Superadmin user already exists: {existing_superadmin.email}")
            return
        
        # Create initial superadmin user
        email = input("Enter superadmin email: ").strip()
        password = input("Enter superadmin password: ").strip()
        
        if not email or not password:
            print("Email and password are required!")
            return
        
        superadmin = User(
            email=email,
            role='superadmin',
            is_active=True
        )
        superadmin.set_password(password)
        
        db.session.add(superadmin)
        db.session.commit()
        
        print(f"Superadmin user created successfully: {email}")

if __name__ == '__main__':
    init_database()
