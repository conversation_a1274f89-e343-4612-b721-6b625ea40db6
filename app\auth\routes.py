from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from app.extensions import db
from .models import User
from .forms import LoginForm, CreateUserForm, ForgotPasswordForm, ChangePasswordForm, ProfileForm
from .decorators import superadmin_required
from werkzeug.security import check_password_hash
import logging

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    # Redirect if already logged in
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    form = LoginForm()

    if form.validate_on_submit():
        try:
            email = form.email.data.lower().strip()
            password = form.password.data
            remember = form.remember_me.data

            # Rate limiting check (basic implementation)
            # TODO: Implement proper rate limiting with Redis

            user = User.query.filter_by(email=email).first()

            if user and user.check_password(password):
                if not user.is_active:
                    flash('Your account has been deactivated. Please contact an administrator.', 'warning')
                    current_app.logger.warning(f'Login attempt for deactivated account: {email}')
                    return render_template('auth/login.html', form=form)

                login_user(user, remember=remember)

                # Update last login timestamp
                user.last_login = db.func.now()
                db.session.commit()

                current_app.logger.info(f'Successful login for user: {email}')
                flash('Welcome back! You have been logged in successfully.', 'success')

                # Redirect to next page or dashboard
                next_page = request.args.get('next')
                if next_page and next_page.startswith('/'):
                    return redirect(next_page)
                return redirect(url_for('dashboard.index'))
            else:
                current_app.logger.warning(f'Failed login attempt for email: {email}')
                flash('Invalid email address or password. Please try again.', 'danger')

        except Exception as e:
            current_app.logger.error(f'Login error: {str(e)}')
            flash('An error occurred during login. Please try again.', 'danger')

    return render_template('auth/login.html', form=form)

@auth_bp.route('/logout')
@login_required
def logout():
    user_email = current_user.email
    logout_user()
    current_app.logger.info(f'User logged out: {user_email}')
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/create-user', methods=['GET', 'POST'])
@login_required
@superadmin_required
def create_user():
    form = CreateUserForm()

    if form.validate_on_submit():
        try:
            # Sanitize and validate input
            email = form.email.data.lower().strip()
            password = form.password.data
            role = form.role.data

            # Double-check email uniqueness
            if User.query.filter_by(email=email).first():
                flash('A user with this email address already exists.', 'danger')
                return render_template('auth/create_user.html', form=form)

            # Create new user
            user = User(
                email=email,
                role=role,
                is_active=True
            )
            user.set_password(password)

            db.session.add(user)
            db.session.commit()

            current_app.logger.info(f'New user created: {email} with role {role} by {current_user.email}')
            flash(f'User {email} has been created successfully with role {role.title()}.', 'success')

            # TODO: Send welcome email if checkbox is checked
            if form.send_welcome_email.data:
                # Implement email sending logic here
                pass

            return redirect(url_for('auth.create_user'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'Error creating user: {str(e)}')
            flash('An error occurred while creating the user. Please try again.', 'danger')

    return render_template('auth/create_user.html', form=form)

@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))

    form = ForgotPasswordForm()

    if form.validate_on_submit():
        try:
            email = form.email.data.lower().strip()
            user = User.query.filter_by(email=email).first()

            # Always show success message for security (don't reveal if email exists)
            flash('If an account with that email exists, we have sent a password reset link.', 'info')

            if user:
                # TODO: Generate reset token and send email
                current_app.logger.info(f'Password reset requested for: {email}')
                # Implement password reset token generation and email sending
                pass
            else:
                current_app.logger.warning(f'Password reset requested for non-existent email: {email}')

            return redirect(url_for('auth.login'))

        except Exception as e:
            current_app.logger.error(f'Error in forgot password: {str(e)}')
            flash('An error occurred. Please try again.', 'danger')

    return render_template('auth/forgot_password.html', form=form)

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    form = ProfileForm(obj=current_user)

    if form.validate_on_submit():
        try:
            # Update user profile
            current_user.first_name = form.first_name.data
            current_user.last_name = form.last_name.data
            current_user.phone = form.phone.data
            current_user.timezone = form.timezone.data
            current_user.email_notifications = form.email_notifications.data
            current_user.sms_notifications = form.sms_notifications.data
            current_user.weekly_reports = form.weekly_reports.data

            db.session.commit()

            current_app.logger.info(f'Profile updated for user: {current_user.email}')

            if request.is_json:
                return jsonify({'success': True, 'message': 'Profile updated successfully'})
            else:
                flash('Your profile has been updated successfully.', 'success')
                return redirect(url_for('auth.profile'))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f'Error updating profile: {str(e)}')

            if request.is_json:
                return jsonify({'success': False, 'message': 'Error updating profile'})
            else:
                flash('An error occurred while updating your profile.', 'danger')

    return render_template('auth/profile.html', form=form)

@auth_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    try:
        current_password = request.json.get('current_password')
        new_password = request.json.get('new_password')

        if not current_password or not new_password:
            return jsonify({'success': False, 'message': 'Missing required fields'}), 400

        # Verify current password
        if not current_user.check_password(current_password):
            return jsonify({'success': False, 'message': 'Current password is incorrect'}), 400

        # Validate new password strength (basic check)
        if len(new_password) < 8:
            return jsonify({'success': False, 'message': 'Password must be at least 8 characters long'}), 400

        # Update password
        current_user.set_password(new_password)
        db.session.commit()

        current_app.logger.info(f'Password changed for user: {current_user.email}')
        return jsonify({'success': True, 'message': 'Password changed successfully'})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'Error changing password: {str(e)}')
        return jsonify({'success': False, 'message': 'An error occurred'}), 500