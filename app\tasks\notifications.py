"""
Notification tasks for sending emails, SMS, and push notifications.
"""

import os
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any, Optional
import logging

from celery import current_task
from celery_app import celery_app
from app.extensions import db
from app.auth.models import User
from app.tracking.models import Vehicle, Alert

logger = logging.getLogger(__name__)

@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def send_email_notification(self, recipient_email: str, subject: str, 
                           body: str, html_body: str = None, 
                           attachments: List[Dict] = None):
    """
    Send email notification with retry logic.
    
    Args:
        recipient_email: Email address to send to
        subject: Email subject
        body: Plain text body
        html_body: HTML body (optional)
        attachments: List of attachment dicts with 'filename' and 'content'
    """
    try:
        # Email configuration from environment
        smtp_server = os.environ.get('SMTP_SERVER', 'localhost')
        smtp_port = int(os.environ.get('SMTP_PORT', '587'))
        smtp_username = os.environ.get('SMTP_USERNAME', '')
        smtp_password = os.environ.get('SMTP_PASSWORD', '')
        smtp_use_tls = os.environ.get('SMTP_USE_TLS', 'true').lower() == 'true'
        sender_email = os.environ.get('SENDER_EMAIL', '<EMAIL>')
        sender_name = os.environ.get('SENDER_NAME', 'Ventsys Fleet Tracking')
        
        # Create message
        msg = MIMEMultipart('alternative')
        msg['Subject'] = subject
        msg['From'] = f"{sender_name} <{sender_email}>"
        msg['To'] = recipient_email
        
        # Add text part
        text_part = MIMEText(body, 'plain')
        msg.attach(text_part)
        
        # Add HTML part if provided
        if html_body:
            html_part = MIMEText(html_body, 'html')
            msg.attach(html_part)
        
        # Add attachments if provided
        if attachments:
            for attachment in attachments:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment['content'])
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {attachment["filename"]}'
                )
                msg.attach(part)
        
        # Send email
        with smtplib.SMTP(smtp_server, smtp_port) as server:
            if smtp_use_tls:
                server.starttls()
            if smtp_username and smtp_password:
                server.login(smtp_username, smtp_password)
            
            server.send_message(msg)
        
        logger.info(f"Email sent successfully to {recipient_email}")
        return {"status": "success", "recipient": recipient_email}
        
    except Exception as exc:
        logger.error(f"Failed to send email to {recipient_email}: {str(exc)}")
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            retry_delay = 60 * (2 ** self.request.retries)
            raise self.retry(exc=exc, countdown=retry_delay)
        
        # Max retries reached
        return {"status": "failed", "recipient": recipient_email, "error": str(exc)}

@celery_app.task(bind=True, max_retries=3, default_retry_delay=30)
def send_sms_notification(self, phone_number: str, message: str):
    """
    Send SMS notification.
    
    Args:
        phone_number: Phone number to send to
        message: SMS message content
    """
    try:
        # SMS service configuration (example with Twilio)
        account_sid = os.environ.get('TWILIO_ACCOUNT_SID')
        auth_token = os.environ.get('TWILIO_AUTH_TOKEN')
        from_number = os.environ.get('TWILIO_FROM_NUMBER')
        
        if not all([account_sid, auth_token, from_number]):
            logger.warning("SMS service not configured - skipping SMS notification")
            return {"status": "skipped", "reason": "SMS service not configured"}
        
        # Import Twilio client (optional dependency)
        try:
            from twilio.rest import Client
            client = Client(account_sid, auth_token)
            
            message = client.messages.create(
                body=message,
                from_=from_number,
                to=phone_number
            )
            
            logger.info(f"SMS sent successfully to {phone_number}")
            return {"status": "success", "recipient": phone_number, "sid": message.sid}
            
        except ImportError:
            logger.error("Twilio library not installed")
            return {"status": "failed", "error": "SMS service unavailable"}
            
    except Exception as exc:
        logger.error(f"Failed to send SMS to {phone_number}: {str(exc)}")
        
        if self.request.retries < self.max_retries:
            raise self.retry(exc=exc, countdown=30)
        
        return {"status": "failed", "recipient": phone_number, "error": str(exc)}

@celery_app.task
def send_geofence_alert(vehicle_id: int, geofence_name: str, alert_type: str):
    """
    Send geofence violation alert to relevant users.
    
    Args:
        vehicle_id: ID of the vehicle
        geofence_name: Name of the geofence
        alert_type: Type of alert ('enter' or 'exit')
    """
    try:
        # Get vehicle information
        vehicle = Vehicle.query.get(vehicle_id)
        if not vehicle:
            logger.error(f"Vehicle {vehicle_id} not found")
            return {"status": "failed", "error": "Vehicle not found"}
        
        # Create alert record
        alert = Alert(
            title=f"Geofence {alert_type.title()} Alert",
            message=f"Vehicle {vehicle.name} ({vehicle.license_plate}) has {alert_type}ed geofence '{geofence_name}'",
            alert_type="geofence",
            vehicle_id=vehicle_id
        )
        db.session.add(alert)
        db.session.commit()
        
        # Get users who should receive alerts (admins and superadmins)
        users = User.query.filter(
            User.role.in_(['admin', 'superadmin']),
            User.is_active == True,
            User.email_notifications == True
        ).all()
        
        # Send notifications
        notification_results = []
        for user in users:
            # Email notification
            email_subject = f"Ventsys Alert: Geofence {alert_type.title()}"
            email_body = f"""
Dear {user.full_name or user.email},

This is an automated alert from the Ventsys Fleet Tracking System.

Alert Details:
- Vehicle: {vehicle.name} ({vehicle.license_plate})
- Geofence: {geofence_name}
- Action: {alert_type.title()}
- Time: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}

Please log in to the system for more details.

Best regards,
Ventsys Fleet Tracking System
            """
            
            email_result = send_email_notification.delay(
                user.email, email_subject, email_body
            )
            notification_results.append({"type": "email", "user": user.email, "task_id": email_result.id})
            
            # SMS notification if enabled and phone number available
            if user.sms_notifications and user.phone:
                sms_message = f"Ventsys Alert: {vehicle.license_plate} {alert_type}ed {geofence_name}"
                sms_result = send_sms_notification.delay(user.phone, sms_message)
                notification_results.append({"type": "sms", "user": user.phone, "task_id": sms_result.id})
        
        logger.info(f"Geofence alert sent for vehicle {vehicle.license_plate}")
        return {"status": "success", "alert_id": alert.id, "notifications": notification_results}
        
    except Exception as exc:
        logger.error(f"Failed to send geofence alert: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def send_speed_alert(vehicle_id: int, current_speed: float, speed_limit: float):
    """
    Send speed violation alert.
    
    Args:
        vehicle_id: ID of the vehicle
        current_speed: Current speed of the vehicle
        speed_limit: Speed limit that was exceeded
    """
    try:
        vehicle = Vehicle.query.get(vehicle_id)
        if not vehicle:
            return {"status": "failed", "error": "Vehicle not found"}
        
        # Create alert
        alert = Alert(
            title="Speed Limit Violation",
            message=f"Vehicle {vehicle.name} ({vehicle.license_plate}) exceeded speed limit: {current_speed:.1f} km/h (limit: {speed_limit:.1f} km/h)",
            alert_type="speed",
            vehicle_id=vehicle_id
        )
        db.session.add(alert)
        db.session.commit()
        
        # Send to admins
        users = User.query.filter(
            User.role.in_(['admin', 'superadmin']),
            User.is_active == True,
            User.email_notifications == True
        ).all()
        
        notification_results = []
        for user in users:
            email_subject = "Ventsys Alert: Speed Limit Violation"
            email_body = f"""
Dear {user.full_name or user.email},

Speed limit violation detected:

Vehicle: {vehicle.name} ({vehicle.license_plate})
Current Speed: {current_speed:.1f} km/h
Speed Limit: {speed_limit:.1f} km/h
Time: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}

Please take appropriate action.

Best regards,
Ventsys Fleet Tracking System
            """
            
            email_result = send_email_notification.delay(
                user.email, email_subject, email_body
            )
            notification_results.append({"type": "email", "user": user.email, "task_id": email_result.id})
        
        return {"status": "success", "alert_id": alert.id, "notifications": notification_results}
        
    except Exception as exc:
        logger.error(f"Failed to send speed alert: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def send_welcome_email(user_id: int, temporary_password: str = None):
    """
    Send welcome email to new user.
    
    Args:
        user_id: ID of the new user
        temporary_password: Temporary password if provided
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return {"status": "failed", "error": "User not found"}
        
        subject = "Welcome to Ventsys Fleet Tracking"
        body = f"""
Dear {user.full_name or user.email},

Welcome to the Ventsys Fleet Tracking System!

Your account has been created with the following details:
- Email: {user.email}
- Role: {user.role.title()}

You can access the system at: [Your System URL]

{f"Your temporary password is: {temporary_password}" if temporary_password else "Please use the password provided by your administrator."}

For security reasons, please change your password after your first login.

If you have any questions, please contact your system administrator.

Best regards,
Ventsys Fleet Tracking Team
        """
        
        result = send_email_notification.delay(user.email, subject, body)
        
        return {"status": "success", "user": user.email, "task_id": result.id}
        
    except Exception as exc:
        logger.error(f"Failed to send welcome email: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def send_bulk_notification(user_ids: List[int], subject: str, message: str, 
                          notification_types: List[str] = None):
    """
    Send bulk notifications to multiple users.
    
    Args:
        user_ids: List of user IDs to notify
        subject: Notification subject
        message: Notification message
        notification_types: Types of notifications to send ['email', 'sms']
    """
    if notification_types is None:
        notification_types = ['email']
    
    try:
        users = User.query.filter(
            User.id.in_(user_ids),
            User.is_active == True
        ).all()
        
        results = []
        for user in users:
            if 'email' in notification_types and user.email_notifications:
                email_result = send_email_notification.delay(user.email, subject, message)
                results.append({"type": "email", "user": user.email, "task_id": email_result.id})
            
            if 'sms' in notification_types and user.sms_notifications and user.phone:
                sms_result = send_sms_notification.delay(user.phone, message)
                results.append({"type": "sms", "user": user.phone, "task_id": sms_result.id})
        
        return {"status": "success", "notifications_sent": len(results), "results": results}
        
    except Exception as exc:
        logger.error(f"Failed to send bulk notifications: {str(exc)}")
        return {"status": "failed", "error": str(exc)}
