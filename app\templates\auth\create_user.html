{% extends 'base.html' %}

{% block title %}Create User - Ventsys{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-person-plus me-2"></i>Create New User</h1>
                <p class="text-muted">Add a new user to the Ventsys system</p>
            </div>
            <div>
                <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="bi bi-person-badge me-2"></i>User Information</h5>
            </div>
            <div class="card-body">
                <form method="POST" id="createUserForm" novalidate>
                    {{ form.hidden_tag() }}

                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                            {{ form.email(class="form-control", placeholder="<EMAIL>") }}
                        </div>
                        {% if form.email.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            The user will receive login credentials at this email address.
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-key"></i></span>
                            {{ form.password(class="form-control", placeholder="Enter secure password") }}
                            <button class="btn btn-outline-secondary"
                                    type="button"
                                    id="togglePassword"
                                    title="Show/Hide Password">
                                <i class="bi bi-eye" id="togglePasswordIcon"></i>
                            </button>
                            <button class="btn btn-outline-info"
                                    type="button"
                                    id="generatePassword"
                                    title="Generate Random Password">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        {% if form.password.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Password should be at least 8 characters long with mixed case, numbers, and symbols.
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 5px;">
                                <div class="progress-bar" id="passwordStrength" style="width: 0%"></div>
                            </div>
                            <small class="text-muted" id="passwordStrengthText">Password strength</small>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.role.label(class="form-label") }}
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-person-gear"></i></span>
                            {{ form.role(class="form-select") }}
                        </div>
                        {% if form.role.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.role.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text" id="roleDescription">
                            Select the appropriate role for this user.
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sendWelcomeEmail" name="send_welcome_email" checked>
                            <label class="form-check-label" for="sendWelcomeEmail">
                                Send welcome email with login instructions
                            </label>
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-success" id="createBtn">
                            <span class="spinner-border spinner-border-sm me-2 d-none" id="createSpinner"></span>
                            <i class="bi bi-person-plus me-2"></i>Create User
                        </button>
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-counterclockwise me-2"></i>Reset Form
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Role Information Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Role Permissions</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">Admin</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check-circle text-success me-1"></i>View live fleet map</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>Access vehicle history</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>Manage geofences</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>View all vehicles</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-info">Customer</h6>
                        <ul class="list-unstyled small">
                            <li><i class="bi bi-check-circle text-success me-1"></i>View assigned vehicles</li>
                            <li><i class="bi bi-check-circle text-success me-1"></i>Basic tracking features</li>
                            <li><i class="bi bi-x-circle text-danger me-1"></i>Cannot manage other users</li>
                            <li><i class="bi bi-x-circle text-danger me-1"></i>Limited system access</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('createUserForm');
    const passwordInput = document.getElementById('password');
    const togglePassword = document.getElementById('togglePassword');
    const togglePasswordIcon = document.getElementById('togglePasswordIcon');
    const generatePassword = document.getElementById('generatePassword');
    const roleSelect = document.getElementById('role');
    const roleDescription = document.getElementById('roleDescription');
    const createBtn = document.getElementById('createBtn');
    const createSpinner = document.getElementById('createSpinner');

    // Role descriptions
    const roleDescriptions = {
        'admin': 'Admins can view live maps, manage vehicles, and access all tracking features.',
        'customer': 'Customers can only view their assigned vehicles and basic tracking information.'
    };

    // Update role description
    roleSelect.addEventListener('change', function() {
        roleDescription.textContent = roleDescriptions[this.value] || 'Select the appropriate role for this user.';
    });

    // Toggle password visibility
    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        if (type === 'password') {
            togglePasswordIcon.className = 'bi bi-eye';
        } else {
            togglePasswordIcon.className = 'bi bi-eye-slash';
        }
    });

    // Generate random password
    generatePassword.addEventListener('click', function() {
        const password = generateRandomPassword();
        passwordInput.value = password;
        passwordInput.setAttribute('type', 'text');
        togglePasswordIcon.className = 'bi bi-eye-slash';
        checkPasswordStrength(password);

        // Show generated password alert
        showAlert('Generated password: ' + password, 'info', 10000);
    });

    // Password strength checker
    passwordInput.addEventListener('input', function() {
        checkPasswordStrength(this.value);
    });

    function generateRandomPassword() {
        const length = 12;
        const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
        let password = "";
        for (let i = 0; i < length; i++) {
            password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return password;
    }

    function checkPasswordStrength(password) {
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');

        let strength = 0;
        let feedback = [];

        if (password.length >= 8) strength += 20;
        else feedback.push('at least 8 characters');

        if (/[a-z]/.test(password)) strength += 20;
        else feedback.push('lowercase letters');

        if (/[A-Z]/.test(password)) strength += 20;
        else feedback.push('uppercase letters');

        if (/[0-9]/.test(password)) strength += 20;
        else feedback.push('numbers');

        if (/[^A-Za-z0-9]/.test(password)) strength += 20;
        else feedback.push('special characters');

        strengthBar.style.width = strength + '%';

        if (strength < 40) {
            strengthBar.className = 'progress-bar bg-danger';
            strengthText.textContent = 'Weak - Add: ' + feedback.join(', ');
        } else if (strength < 80) {
            strengthBar.className = 'progress-bar bg-warning';
            strengthText.textContent = 'Medium - Add: ' + feedback.join(', ');
        } else {
            strengthBar.className = 'progress-bar bg-success';
            strengthText.textContent = 'Strong password';
        }
    }

    // Form submission
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        if (!form.checkValidity()) {
            e.stopPropagation();
            form.classList.add('was-validated');
            return;
        }

        // Show loading state
        createBtn.disabled = true;
        createSpinner.classList.remove('d-none');

        // Submit form normally (let Flask handle it)
        form.submit();
    });

    // Initialize role description
    if (roleSelect.value) {
        roleDescription.textContent = roleDescriptions[roleSelect.value];
    }
});
</script>
{% endblock %}