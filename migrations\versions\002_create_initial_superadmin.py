"""Create initial superadmin user

Revision ID: 002
Revises: 001
Create Date: 2025-01-23 10:05:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime
from werkzeug.security import generate_password_hash

# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    """Create initial superadmin user."""
    
    # Create a connection to execute raw SQL
    connection = op.get_bind()
    
    # Check if superadmin already exists
    result = connection.execute(
        sa.text("SELECT COUNT(*) FROM user WHERE role = 'superadmin'")
    ).scalar()
    
    if result == 0:
        # Create initial superadmin user
        password_hash = generate_password_hash('admin123')  # Change this in production!
        
        connection.execute(
            sa.text("""
                INSERT INTO user (
                    email, password_hash, role, is_active, 
                    first_name, last_name, timezone,
                    email_notifications, sms_notifications, weekly_reports,
                    created_at
                ) VALUES (
                    :email, :password_hash, :role, :is_active,
                    :first_name, :last_name, :timezone,
                    :email_notifications, :sms_notifications, :weekly_reports,
                    :created_at
                )
            """),
            {
                'email': '<EMAIL>',
                'password_hash': password_hash,
                'role': 'superadmin',
                'is_active': True,
                'first_name': 'System',
                'last_name': 'Administrator',
                'timezone': 'UTC',
                'email_notifications': True,
                'sms_notifications': False,
                'weekly_reports': True,
                'created_at': datetime.utcnow()
            }
        )
        
        print("Created initial superadmin user: <EMAIL> / admin123")
        print("IMPORTANT: Change the default password after first login!")
    else:
        print("Superadmin user already exists, skipping creation.")


def downgrade():
    """Remove initial superadmin user."""
    
    connection = op.get_bind()
    
    # Remove the initial superadmin user
    connection.execute(
        sa.text("DELETE FROM user WHERE email = '<EMAIL>' AND role = 'superadmin'")
    )
    
    print("Removed initial superadmin user.")
