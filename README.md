# Ventsys Fleet Tracking System

A Flask-based fleet tracking system with real-time GPS monitoring, geofencing, and user management.

## Features

- **User Authentication**: Role-based access control (superadmin, admin, customer)
- **Real-time Tracking**: GPS location tracking for vehicles
- **Geofencing**: Alert system for vehicles leaving designated areas
- **Dashboard**: Role-specific dashboards for different user types
- **MQTT Support**: IoT device communication via MQTT

## Quick Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Initialize Database

```bash
python init_db.py
```

This will create the database tables and prompt you to create a superadmin user.

### 3. Run the Application

```bash
python run.py
```

The application will be available at `http://localhost:5000`

## Configuration

The application supports different environments:

- **Development** (default): Uses SQLite database
- **Production**: Configure via environment variables
- **Testing**: In-memory SQLite database

Set the `FLASK_CONFIG` environment variable to change the configuration:

```bash
export FLASK_CONFIG=production
```

## Environment Variables

- `SECRET_KEY`: Flask secret key for sessions
- `DATABASE_URL`: Database connection string
- `FLASK_CONFIG`: Configuration environment (development/production/testing)

## User Roles

- **Superadmin**: Full system access, can create users
- **Admin**: Can view live map and manage fleet
- **Customer**: Can view assigned vehicles only

## API Endpoints

### GPS Data Submission
```
POST /tracking/api/gps
Content-Type: application/json

{
    "plate": "ABC123",
    "lat": 40.7128,
    "lng": -74.0060,
    "speed": 25.5
}
```

## Development

### Running Tests
```bash
pytest
```

### Database Migrations
```bash
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

## Production Deployment

1. Set environment variables for production
2. Use a production WSGI server like Gunicorn:
   ```bash
   gunicorn -w 4 -b 0.0.0.0:8000 run:app
   ```
3. Configure a reverse proxy (nginx) for static files and SSL
