"""Create demo data for development and testing

Revision ID: 003
Revises: 002
Create Date: 2025-01-23 10:10:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash
import json

# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    """Create demo data for development and testing."""
    
    connection = op.get_bind()
    
    # Check if we're in development mode (you might want to add a config check here)
    # For now, we'll create demo data by default
    
    # Create demo users
    demo_users = [
        {
            'email': '<EMAIL>',
            'password': 'manager123',
            'role': 'admin',
            'first_name': 'Fleet',
            'last_name': 'Manager'
        },
        {
            'email': '<EMAIL>',
            'password': 'customer123',
            'role': 'customer',
            'first_name': 'Demo',
            'last_name': 'Customer'
        }
    ]
    
    for user_data in demo_users:
        # Check if user already exists
        result = connection.execute(
            sa.text("SELECT COUNT(*) FROM user WHERE email = :email"),
            {'email': user_data['email']}
        ).scalar()
        
        if result == 0:
            password_hash = generate_password_hash(user_data['password'])
            
            connection.execute(
                sa.text("""
                    INSERT INTO user (
                        email, password_hash, role, is_active,
                        first_name, last_name, timezone,
                        email_notifications, sms_notifications, weekly_reports,
                        created_at
                    ) VALUES (
                        :email, :password_hash, :role, :is_active,
                        :first_name, :last_name, :timezone,
                        :email_notifications, :sms_notifications, :weekly_reports,
                        :created_at
                    )
                """),
                {
                    'email': user_data['email'],
                    'password_hash': password_hash,
                    'role': user_data['role'],
                    'is_active': True,
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'timezone': 'UTC',
                    'email_notifications': True,
                    'sms_notifications': False,
                    'weekly_reports': True,
                    'created_at': datetime.utcnow()
                }
            )
            print(f"Created demo user: {user_data['email']} / {user_data['password']}")
    
    # Create demo vehicles
    demo_vehicles = [
        {
            'name': 'Delivery Truck 1',
            'license_plate': 'VTS-001',
            'description': 'Main delivery truck for downtown routes'
        },
        {
            'name': 'Service Van A',
            'license_plate': 'VTS-002',
            'description': 'Service vehicle for maintenance calls'
        },
        {
            'name': 'Fleet Car 1',
            'license_plate': 'VTS-003',
            'description': 'Company car for sales team'
        }
    ]
    
    for vehicle_data in demo_vehicles:
        # Check if vehicle already exists
        result = connection.execute(
            sa.text("SELECT COUNT(*) FROM vehicle WHERE license_plate = :plate"),
            {'plate': vehicle_data['license_plate']}
        ).scalar()
        
        if result == 0:
            connection.execute(
                sa.text("""
                    INSERT INTO vehicle (
                        name, license_plate, is_active, description, created_at, last_updated
                    ) VALUES (
                        :name, :license_plate, :is_active, :description, :created_at, :last_updated
                    )
                """),
                {
                    'name': vehicle_data['name'],
                    'license_plate': vehicle_data['license_plate'],
                    'is_active': True,
                    'description': vehicle_data['description'],
                    'created_at': datetime.utcnow(),
                    'last_updated': datetime.utcnow()
                }
            )
            print(f"Created demo vehicle: {vehicle_data['license_plate']} - {vehicle_data['name']}")
    
    # Create demo geofence
    demo_geofence = {
        'name': 'Downtown Area',
        'description': 'Main business district geofence',
        'coordinates': {
            "type": "Polygon",
            "coordinates": [[
                [-74.0059, 40.7128],  # NYC coordinates as example
                [-74.0059, 40.7200],
                [-73.9959, 40.7200],
                [-73.9959, 40.7128],
                [-74.0059, 40.7128]
            ]]
        }
    }
    
    # Check if geofence already exists
    result = connection.execute(
        sa.text("SELECT COUNT(*) FROM geofence WHERE name = :name"),
        {'name': demo_geofence['name']}
    ).scalar()
    
    if result == 0:
        # Get superadmin user ID
        superadmin_result = connection.execute(
            sa.text("SELECT id FROM user WHERE role = 'superadmin' LIMIT 1")
        ).fetchone()
        
        if superadmin_result:
            superadmin_id = superadmin_result[0]
            
            connection.execute(
                sa.text("""
                    INSERT INTO geofence (
                        name, description, coordinates, is_active,
                        fence_type, alert_on_enter, alert_on_exit,
                        created_at, created_by
                    ) VALUES (
                        :name, :description, :coordinates, :is_active,
                        :fence_type, :alert_on_enter, :alert_on_exit,
                        :created_at, :created_by
                    )
                """),
                {
                    'name': demo_geofence['name'],
                    'description': demo_geofence['description'],
                    'coordinates': json.dumps(demo_geofence['coordinates']),
                    'is_active': True,
                    'fence_type': 'inclusion',
                    'alert_on_enter': False,
                    'alert_on_exit': True,
                    'created_at': datetime.utcnow(),
                    'created_by': superadmin_id
                }
            )
            print(f"Created demo geofence: {demo_geofence['name']}")
    
    # Create some sample location data for the first vehicle
    vehicle_result = connection.execute(
        sa.text("SELECT id FROM vehicle WHERE license_plate = 'VTS-001' LIMIT 1")
    ).fetchone()
    
    if vehicle_result:
        vehicle_id = vehicle_result[0]
        
        # Create sample locations over the past 24 hours
        base_time = datetime.utcnow() - timedelta(hours=24)
        base_lat = 40.7128  # NYC latitude
        base_lng = -74.0060  # NYC longitude
        
        for i in range(0, 24, 2):  # Every 2 hours
            timestamp = base_time + timedelta(hours=i)
            # Simulate movement by slightly changing coordinates
            lat = base_lat + (i * 0.001)
            lng = base_lng + (i * 0.001)
            speed = 30 + (i % 10)  # Varying speed
            
            connection.execute(
                sa.text("""
                    INSERT INTO location (
                        vehicle_id, lat, lng, timestamp, speed, heading,
                        accuracy, is_valid, source
                    ) VALUES (
                        :vehicle_id, :lat, :lng, :timestamp, :speed, :heading,
                        :accuracy, :is_valid, :source
                    )
                """),
                {
                    'vehicle_id': vehicle_id,
                    'lat': lat,
                    'lng': lng,
                    'timestamp': timestamp,
                    'speed': speed,
                    'heading': (i * 15) % 360,  # Varying heading
                    'accuracy': 5.0,
                    'is_valid': True,
                    'source': 'gps'
                }
            )
        
        print(f"Created sample location data for vehicle VTS-001")
    
    print("Demo data creation completed!")
    print("Demo credentials:")
    print("  Superadmin: <EMAIL> / admin123")
    print("  Admin: <EMAIL> / manager123")
    print("  Customer: <EMAIL> / customer123")
    print("IMPORTANT: Change all default passwords in production!")


def downgrade():
    """Remove demo data."""
    
    connection = op.get_bind()
    
    # Remove demo location data
    connection.execute(
        sa.text("""
            DELETE FROM location 
            WHERE vehicle_id IN (
                SELECT id FROM vehicle 
                WHERE license_plate IN ('VTS-001', 'VTS-002', 'VTS-003')
            )
        """)
    )
    
    # Remove demo geofences
    connection.execute(
        sa.text("DELETE FROM geofence WHERE name = 'Downtown Area'")
    )
    
    # Remove demo vehicles
    connection.execute(
        sa.text("DELETE FROM vehicle WHERE license_plate IN ('VTS-001', 'VTS-002', 'VTS-003')")
    )
    
    # Remove demo users (but keep superadmin)
    connection.execute(
        sa.text("""
            DELETE FROM user 
            WHERE email IN ('<EMAIL>', '<EMAIL>')
        """)
    )
    
    print("Demo data removed.")
