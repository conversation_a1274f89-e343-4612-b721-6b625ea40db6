{% extends 'base.html' %}

{% block title %}User Profile - Ventsys{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1><i class="bi bi-person-circle me-2"></i>User Profile</h1>
                <p class="text-muted">Manage your account settings and preferences</p>
            </div>
            <div>
                <a href="{{ url_for('dashboard.index') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-person-badge me-2"></i>Account Information</h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="bi bi-person-circle text-primary" style="font-size: 4rem;"></i>
                </div>
                <h5>{{ current_user.email }}</h5>
                <span class="badge bg-primary">{{ current_user.role.title() }}</span>
                <hr>
                <div class="text-start">
                    <p><strong>Account Status:</strong> 
                        {% if current_user.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-danger">Inactive</span>
                        {% endif %}
                    </p>
                    <p><strong>Member Since:</strong> {{ current_user.created_at.strftime('%B %Y') if current_user.created_at else 'N/A' }}</p>
                    <p><strong>Last Login:</strong> {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') if current_user.last_login else 'N/A' }}</p>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="bi bi-shield-check me-2"></i>Security</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-warning btn-sm" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="bi bi-key me-1"></i>Change Password
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="downloadAccountData()">
                        <i class="bi bi-download me-1"></i>Download My Data
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-gear me-2"></i>Profile Settings</h5>
            </div>
            <div class="card-body">
                <form id="profileForm" method="POST">
                    {{ csrf_token() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="firstName" class="form-label">First Name</label>
                                <input type="text" class="form-control" id="firstName" name="first_name" 
                                       value="{{ current_user.first_name or '' }}" placeholder="Enter first name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lastName" class="form-label">Last Name</label>
                                <input type="text" class="form-control" id="lastName" name="last_name" 
                                       value="{{ current_user.last_name or '' }}" placeholder="Enter last name">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="phone" name="phone" 
                               value="{{ current_user.phone or '' }}" placeholder="Enter phone number">
                    </div>
                    
                    <div class="mb-3">
                        <label for="timezone" class="form-label">Timezone</label>
                        <select class="form-select" id="timezone" name="timezone">
                            <option value="UTC">UTC</option>
                            <option value="America/New_York">Eastern Time</option>
                            <option value="America/Chicago">Central Time</option>
                            <option value="America/Denver">Mountain Time</option>
                            <option value="America/Los_Angeles">Pacific Time</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Notification Preferences</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="emailNotifications" name="email_notifications" checked>
                            <label class="form-check-label" for="emailNotifications">
                                Email notifications for alerts
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="smsNotifications" name="sms_notifications">
                            <label class="form-check-label" for="smsNotifications">
                                SMS notifications for critical alerts
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="weeklyReports" name="weekly_reports" checked>
                            <label class="form-check-label" for="weeklyReports">
                                Weekly activity reports
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="reset" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-counterclockwise me-1"></i>Reset
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-lg me-1"></i>Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        {% if current_user.role in ['admin', 'superadmin'] %}
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="bi bi-activity me-2"></i>Recent Activity</h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-box-arrow-in-right text-success me-2"></i>
                            Logged in from 192.168.1.100
                        </div>
                        <small class="text-muted">2 hours ago</small>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-map text-primary me-2"></i>
                            Viewed live map
                        </div>
                        <small class="text-muted">3 hours ago</small>
                    </div>
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="bi bi-person-plus text-info me-2"></i>
                            Created new user account
                        </div>
                        <small class="text-muted">1 day ago</small>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-key me-2"></i>Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label for="currentPassword" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="currentPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newPassword" required>
                        <div class="form-text">Password must be at least 8 characters long.</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="changePassword()">Change Password</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const profileForm = document.getElementById('profileForm');
    
    // Profile form submission
    profileForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        fetch('/auth/profile', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Profile updated successfully!', 'success');
            } else {
                showAlert('Error updating profile: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred while updating your profile.', 'danger');
        });
    });
});

function changePassword() {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    if (newPassword !== confirmPassword) {
        showAlert('New passwords do not match.', 'danger');
        return;
    }
    
    if (newPassword.length < 8) {
        showAlert('Password must be at least 8 characters long.', 'danger');
        return;
    }
    
    // Simulate password change (replace with actual implementation)
    showAlert('Password changed successfully!', 'success');
    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
    document.getElementById('changePasswordForm').reset();
}

function downloadAccountData() {
    // Simulate data download (replace with actual implementation)
    showAlert('Your account data download has been initiated. You will receive an email when ready.', 'info');
}
</script>
{% endblock %}
