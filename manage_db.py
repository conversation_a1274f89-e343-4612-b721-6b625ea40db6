#!/usr/bin/env python3
"""
Database management script for Ventsys Flask application.

This script provides convenient commands for managing database migrations,
including creating, applying, and rolling back migrations.

Usage:
    python manage_db.py init          # Initialize migration repository
    python manage_db.py migrate       # Create new migration
    python manage_db.py upgrade       # Apply migrations
    python manage_db.py downgrade     # Rollback migrations
    python manage_db.py current       # Show current revision
    python manage_db.py history       # Show migration history
    python manage_db.py reset         # Reset database (DANGEROUS!)
"""

import os
import sys
import click
from flask import Flask
from flask_migrate import Migrate, init, migrate, upgrade, downgrade, current, history
from app import create_app
from app.extensions import db

# Create Flask app
app = create_app()
migrate_obj = Migrate(app, db)

@click.group()
def cli():
    """Database management commands."""
    pass

@cli.command()
def init_db():
    """Initialize the migration repository."""
    with app.app_context():
        try:
            init()
            click.echo("Migration repository initialized successfully.")
        except Exception as e:
            click.echo(f"Error initializing migration repository: {e}", err=True)
            sys.exit(1)

@cli.command()
@click.option('--message', '-m', help='Migration message')
def create_migration(message):
    """Create a new migration."""
    with app.app_context():
        try:
            if not message:
                message = click.prompt('Enter migration message')
            
            migrate(message=message)
            click.echo(f"Migration created: {message}")
        except Exception as e:
            click.echo(f"Error creating migration: {e}", err=True)
            sys.exit(1)

@cli.command()
@click.option('--revision', '-r', help='Target revision (default: head)')
def apply_migrations(revision):
    """Apply migrations to the database."""
    with app.app_context():
        try:
            if revision:
                upgrade(revision=revision)
                click.echo(f"Database upgraded to revision: {revision}")
            else:
                upgrade()
                click.echo("Database upgraded to latest revision.")
        except Exception as e:
            click.echo(f"Error applying migrations: {e}", err=True)
            sys.exit(1)

@cli.command()
@click.option('--revision', '-r', help='Target revision (default: -1)')
def rollback_migrations(revision):
    """Rollback migrations."""
    with app.app_context():
        try:
            if not revision:
                revision = '-1'
            
            # Confirm rollback
            if not click.confirm(f'Are you sure you want to rollback to revision {revision}?'):
                click.echo("Rollback cancelled.")
                return
            
            downgrade(revision=revision)
            click.echo(f"Database downgraded to revision: {revision}")
        except Exception as e:
            click.echo(f"Error rolling back migrations: {e}", err=True)
            sys.exit(1)

@cli.command()
def show_current():
    """Show current database revision."""
    with app.app_context():
        try:
            rev = current()
            if rev:
                click.echo(f"Current revision: {rev}")
            else:
                click.echo("No migrations applied yet.")
        except Exception as e:
            click.echo(f"Error getting current revision: {e}", err=True)
            sys.exit(1)

@cli.command()
@click.option('--verbose', '-v', is_flag=True, help='Show detailed history')
def show_history(verbose):
    """Show migration history."""
    with app.app_context():
        try:
            history_output = history(verbose=verbose)
            if history_output:
                click.echo("Migration history:")
                click.echo(history_output)
            else:
                click.echo("No migration history found.")
        except Exception as e:
            click.echo(f"Error getting migration history: {e}", err=True)
            sys.exit(1)

@cli.command()
@click.option('--force', is_flag=True, help='Force reset without confirmation')
def reset_database(force):
    """Reset the database (DANGEROUS - will delete all data!)."""
    if not force:
        click.echo("WARNING: This will delete ALL data in the database!")
        if not click.confirm('Are you absolutely sure you want to continue?'):
            click.echo("Database reset cancelled.")
            return
        
        if not click.confirm('This action cannot be undone. Continue?'):
            click.echo("Database reset cancelled.")
            return
    
    with app.app_context():
        try:
            # Drop all tables
            db.drop_all()
            click.echo("All tables dropped.")
            
            # Recreate tables
            db.create_all()
            click.echo("Tables recreated.")
            
            # Apply all migrations
            upgrade()
            click.echo("Migrations applied.")
            
            click.echo("Database reset completed successfully.")
        except Exception as e:
            click.echo(f"Error resetting database: {e}", err=True)
            sys.exit(1)

@cli.command()
def create_tables():
    """Create all database tables (without migrations)."""
    with app.app_context():
        try:
            db.create_all()
            click.echo("All tables created successfully.")
        except Exception as e:
            click.echo(f"Error creating tables: {e}", err=True)
            sys.exit(1)

@cli.command()
def drop_tables():
    """Drop all database tables."""
    if not click.confirm('Are you sure you want to drop all tables?'):
        click.echo("Operation cancelled.")
        return
    
    with app.app_context():
        try:
            db.drop_all()
            click.echo("All tables dropped successfully.")
        except Exception as e:
            click.echo(f"Error dropping tables: {e}", err=True)
            sys.exit(1)

@cli.command()
def check_migrations():
    """Check if there are pending migrations."""
    with app.app_context():
        try:
            from flask_migrate import get_head_revision, get_current_revision
            
            head_rev = get_head_revision()
            current_rev = get_current_revision()
            
            if head_rev == current_rev:
                click.echo("Database is up to date.")
            else:
                click.echo(f"Pending migrations detected:")
                click.echo(f"  Current revision: {current_rev or 'None'}")
                click.echo(f"  Latest revision: {head_rev}")
                click.echo("Run 'python manage_db.py apply_migrations' to update.")
        except Exception as e:
            click.echo(f"Error checking migrations: {e}", err=True)
            sys.exit(1)

@cli.command()
@click.argument('sql_file', type=click.Path(exists=True))
def execute_sql(sql_file):
    """Execute SQL commands from a file."""
    with app.app_context():
        try:
            with open(sql_file, 'r') as f:
                sql_commands = f.read()
            
            # Split by semicolon and execute each command
            commands = [cmd.strip() for cmd in sql_commands.split(';') if cmd.strip()]
            
            for command in commands:
                db.session.execute(command)
            
            db.session.commit()
            click.echo(f"SQL file executed successfully: {sql_file}")
        except Exception as e:
            db.session.rollback()
            click.echo(f"Error executing SQL file: {e}", err=True)
            sys.exit(1)

@cli.command()
def backup_database():
    """Create a backup of the database."""
    with app.app_context():
        try:
            from datetime import datetime
            import subprocess
            
            # Get database URL
            db_url = app.config['SQLALCHEMY_DATABASE_URI']
            
            if db_url.startswith('sqlite'):
                # SQLite backup
                db_path = db_url.replace('sqlite:///', '')
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                
                import shutil
                shutil.copy2(db_path, backup_name)
                click.echo(f"SQLite database backed up to: {backup_name}")
            
            elif db_url.startswith('postgresql'):
                # PostgreSQL backup
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
                
                # Extract connection details from URL
                # This is a simplified version - you might need more robust URL parsing
                cmd = f"pg_dump {db_url} > {backup_name}"
                subprocess.run(cmd, shell=True, check=True)
                click.echo(f"PostgreSQL database backed up to: {backup_name}")
            
            else:
                click.echo("Backup not implemented for this database type.")
        
        except Exception as e:
            click.echo(f"Error creating backup: {e}", err=True)
            sys.exit(1)

if __name__ == '__main__':
    cli()
