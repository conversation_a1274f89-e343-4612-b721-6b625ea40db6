"""
Data processing tasks for maintenance, cleanup, and analysis.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List
from sqlalchemy import func, and_, or_

from celery_app import celery_app
from app.extensions import db
from app.tracking.models import Vehicle, Location, Alert
from app.auth.models import User

logger = logging.getLogger(__name__)

@celery_app.task
def cleanup_old_locations(days_to_keep: int = 90):
    """
    Clean up old location data to manage database size.
    
    Args:
        days_to_keep: Number of days of location data to retain
    """
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        # Count locations to be deleted
        locations_to_delete = Location.query.filter(
            Location.timestamp < cutoff_date
        ).count()
        
        if locations_to_delete == 0:
            logger.info("No old locations to clean up")
            return {"status": "success", "deleted_count": 0}
        
        # Delete old locations in batches to avoid locking the database
        batch_size = 1000
        total_deleted = 0
        
        while True:
            batch = Location.query.filter(
                Location.timestamp < cutoff_date
            ).limit(batch_size).all()
            
            if not batch:
                break
            
            for location in batch:
                db.session.delete(location)
            
            db.session.commit()
            total_deleted += len(batch)
            
            logger.info(f"Deleted {len(batch)} locations (total: {total_deleted})")
        
        logger.info(f"Cleanup completed: deleted {total_deleted} old locations")
        return {"status": "success", "deleted_count": total_deleted}
        
    except Exception as exc:
        db.session.rollback()
        logger.error(f"Failed to cleanup old locations: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def cleanup_old_alerts(days_to_keep: int = 30):
    """
    Clean up old resolved alerts.
    
    Args:
        days_to_keep: Number of days of alert data to retain
    """
    try:
        cutoff_date = datetime.utcnow() - timedelta(days=days_to_keep)
        
        # Delete old acknowledged alerts
        deleted_count = Alert.query.filter(
            and_(
                Alert.created_at < cutoff_date,
                Alert.acknowledged_at.isnot(None),
                Alert.is_active == False
            )
        ).delete()
        
        db.session.commit()
        
        logger.info(f"Cleaned up {deleted_count} old alerts")
        return {"status": "success", "deleted_count": deleted_count}
        
    except Exception as exc:
        db.session.rollback()
        logger.error(f"Failed to cleanup old alerts: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def check_vehicle_status():
    """
    Check vehicle status and create alerts for inactive vehicles.
    """
    try:
        # Consider vehicles inactive if no GPS data for 30 minutes
        inactive_threshold = datetime.utcnow() - timedelta(minutes=30)
        
        # Find vehicles that should be active but haven't reported recently
        inactive_vehicles = Vehicle.query.filter(
            and_(
                Vehicle.is_active == True,
                or_(
                    Vehicle.last_updated < inactive_threshold,
                    Vehicle.last_updated.is_(None)
                )
            )
        ).all()
        
        alerts_created = 0
        for vehicle in inactive_vehicles:
            # Check if we already have a recent inactive alert for this vehicle
            existing_alert = Alert.query.filter(
                and_(
                    Alert.vehicle_id == vehicle.id,
                    Alert.alert_type == 'inactive',
                    Alert.created_at > datetime.utcnow() - timedelta(hours=1),
                    Alert.is_active == True
                )
            ).first()
            
            if not existing_alert:
                # Create new inactive alert
                alert = Alert(
                    title="Vehicle Inactive",
                    message=f"Vehicle {vehicle.name} ({vehicle.license_plate}) has not reported location data for over 30 minutes",
                    alert_type="inactive",
                    vehicle_id=vehicle.id
                )
                db.session.add(alert)
                alerts_created += 1
        
        db.session.commit()
        
        logger.info(f"Vehicle status check completed: {alerts_created} new inactive alerts created")
        return {
            "status": "success", 
            "inactive_vehicles": len(inactive_vehicles),
            "alerts_created": alerts_created
        }
        
    except Exception as exc:
        db.session.rollback()
        logger.error(f"Failed to check vehicle status: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def calculate_vehicle_statistics(vehicle_id: int, date_from: str, date_to: str):
    """
    Calculate comprehensive statistics for a vehicle over a date range.
    
    Args:
        vehicle_id: ID of the vehicle
        date_from: Start date (ISO format)
        date_to: End date (ISO format)
    """
    try:
        vehicle = Vehicle.query.get(vehicle_id)
        if not vehicle:
            return {"status": "failed", "error": "Vehicle not found"}
        
        start_date = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
        end_date = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
        
        # Get locations for the date range
        locations = Location.query.filter(
            and_(
                Location.vehicle_id == vehicle_id,
                Location.timestamp >= start_date,
                Location.timestamp <= end_date,
                Location.is_valid == True
            )
        ).order_by(Location.timestamp).all()
        
        if not locations:
            return {
                "status": "success",
                "vehicle_id": vehicle_id,
                "statistics": {"total_distance": 0, "total_time": 0, "locations_count": 0}
            }
        
        # Calculate statistics
        total_distance = 0.0
        max_speed = 0.0
        speeds = []
        
        for i in range(1, len(locations)):
            prev_loc = locations[i-1]
            curr_loc = locations[i]
            
            # Calculate distance using Haversine formula (simplified)
            lat_diff = curr_loc.lat - prev_loc.lat
            lng_diff = curr_loc.lng - prev_loc.lng
            distance = ((lat_diff ** 2) + (lng_diff ** 2)) ** 0.5 * 111  # Rough km conversion
            total_distance += distance
            
            # Track speeds
            if curr_loc.speed:
                speeds.append(curr_loc.speed)
                max_speed = max(max_speed, curr_loc.speed)
        
        # Calculate time duration
        total_time = (locations[-1].timestamp - locations[0].timestamp).total_seconds()
        
        statistics = {
            "vehicle_id": vehicle_id,
            "vehicle_name": vehicle.name,
            "license_plate": vehicle.license_plate,
            "date_range": {"from": date_from, "to": date_to},
            "total_distance_km": round(total_distance, 2),
            "total_time_seconds": int(total_time),
            "total_time_hours": round(total_time / 3600, 2),
            "locations_count": len(locations),
            "max_speed_kmh": round(max_speed, 1) if max_speed > 0 else 0,
            "avg_speed_kmh": round(sum(speeds) / len(speeds), 1) if speeds else 0,
            "first_location": locations[0].timestamp.isoformat(),
            "last_location": locations[-1].timestamp.isoformat()
        }
        
        logger.info(f"Statistics calculated for vehicle {vehicle.license_plate}")
        return {"status": "success", "statistics": statistics}
        
    except Exception as exc:
        logger.error(f"Failed to calculate vehicle statistics: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def detect_anomalies(vehicle_id: int = None):
    """
    Detect anomalies in vehicle data (unusual speeds, locations, etc.).
    
    Args:
        vehicle_id: Specific vehicle ID to check, or None for all vehicles
    """
    try:
        # Get vehicles to check
        if vehicle_id:
            vehicles = [Vehicle.query.get(vehicle_id)]
            if not vehicles[0]:
                return {"status": "failed", "error": "Vehicle not found"}
        else:
            vehicles = Vehicle.query.filter(Vehicle.is_active == True).all()
        
        anomalies_detected = []
        
        for vehicle in vehicles:
            # Get recent locations (last 24 hours)
            recent_threshold = datetime.utcnow() - timedelta(hours=24)
            recent_locations = Location.query.filter(
                and_(
                    Location.vehicle_id == vehicle.id,
                    Location.timestamp > recent_threshold,
                    Location.is_valid == True
                )
            ).order_by(Location.timestamp).all()
            
            if len(recent_locations) < 2:
                continue
            
            # Check for speed anomalies
            speeds = [loc.speed for loc in recent_locations if loc.speed]
            if speeds:
                avg_speed = sum(speeds) / len(speeds)
                max_speed = max(speeds)
                
                # Flag if max speed is more than 3x average speed and > 100 km/h
                if max_speed > avg_speed * 3 and max_speed > 100:
                    anomalies_detected.append({
                        "vehicle_id": vehicle.id,
                        "type": "speed_anomaly",
                        "description": f"Unusual speed detected: {max_speed:.1f} km/h (avg: {avg_speed:.1f} km/h)",
                        "severity": "high" if max_speed > 150 else "medium"
                    })
            
            # Check for location jumps (teleportation)
            for i in range(1, len(recent_locations)):
                prev_loc = recent_locations[i-1]
                curr_loc = recent_locations[i]
                
                # Calculate distance and time
                lat_diff = curr_loc.lat - prev_loc.lat
                lng_diff = curr_loc.lng - prev_loc.lng
                distance = ((lat_diff ** 2) + (lng_diff ** 2)) ** 0.5 * 111  # km
                
                time_diff = (curr_loc.timestamp - prev_loc.timestamp).total_seconds() / 3600  # hours
                
                if time_diff > 0:
                    implied_speed = distance / time_diff
                    
                    # Flag if implied speed > 200 km/h (likely GPS error or teleportation)
                    if implied_speed > 200:
                        anomalies_detected.append({
                            "vehicle_id": vehicle.id,
                            "type": "location_jump",
                            "description": f"Possible GPS error: {distance:.1f} km in {time_diff:.2f} hours (implied speed: {implied_speed:.1f} km/h)",
                            "severity": "medium"
                        })
        
        # Create alerts for high-severity anomalies
        alerts_created = 0
        for anomaly in anomalies_detected:
            if anomaly["severity"] == "high":
                alert = Alert(
                    title="Data Anomaly Detected",
                    message=anomaly["description"],
                    alert_type="anomaly",
                    vehicle_id=anomaly["vehicle_id"]
                )
                db.session.add(alert)
                alerts_created += 1
        
        db.session.commit()
        
        logger.info(f"Anomaly detection completed: {len(anomalies_detected)} anomalies found, {alerts_created} alerts created")
        return {
            "status": "success",
            "anomalies_detected": len(anomalies_detected),
            "alerts_created": alerts_created,
            "anomalies": anomalies_detected
        }
        
    except Exception as exc:
        db.session.rollback()
        logger.error(f"Failed to detect anomalies: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def optimize_database():
    """
    Perform database optimization tasks.
    """
    try:
        # Update database statistics
        db.session.execute("ANALYZE")
        
        # Clean up expired sessions (if using database sessions)
        # This would depend on your session configuration
        
        logger.info("Database optimization completed")
        return {"status": "success", "message": "Database optimized"}
        
    except Exception as exc:
        logger.error(f"Failed to optimize database: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def process_bulk_location_data(location_data_batch: List[Dict]):
    """
    Process a batch of location data efficiently.

    Args:
        location_data_batch: List of location data dictionaries
    """
    try:
        processed_count = 0
        errors = []

        for location_data in location_data_batch:
            try:
                # Validate and process each location
                vehicle = Vehicle.query.filter_by(
                    license_plate=location_data.get('plate', '').upper()
                ).first()

                if not vehicle:
                    errors.append(f"Vehicle not found: {location_data.get('plate')}")
                    continue

                location = Location(
                    vehicle_id=vehicle.id,
                    lat=float(location_data['lat']),
                    lng=float(location_data['lng']),
                    speed=location_data.get('speed'),
                    heading=location_data.get('heading'),
                    accuracy=location_data.get('accuracy'),
                    timestamp=datetime.fromisoformat(location_data.get('timestamp', datetime.utcnow().isoformat()))
                )

                db.session.add(location)
                processed_count += 1

            except Exception as e:
                errors.append(f"Error processing location: {str(e)}")

        db.session.commit()

        logger.info(f"Bulk location processing completed: {processed_count} processed, {len(errors)} errors")
        return {
            "status": "success",
            "processed_count": processed_count,
            "error_count": len(errors),
            "errors": errors[:10]  # Return first 10 errors
        }

    except Exception as exc:
        db.session.rollback()
        logger.error(f"Failed to process bulk location data: {str(exc)}")
        return {"status": "failed", "error": str(exc)}
