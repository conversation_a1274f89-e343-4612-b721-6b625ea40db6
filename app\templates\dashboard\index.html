{% extends 'base.html' %}

{% block title %}Dashboard - Ventsys{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>Dashboard</h1>
        <p>Welcome to the Ventsys Fleet Tracking System, {{ current_user.email }}!</p>
        <p>Your role: <span class="badge bg-primary">{{ current_user.role.title() }}</span></p>
    </div>
</div>

<div class="row mt-4">
    {% if current_user.role in ['admin', 'superadmin'] %}
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>Fleet Management</h5>
            </div>
            <div class="card-body">
                <p>Monitor and manage your fleet in real-time.</p>
                <a href="{{ url_for('tracking.live_map') }}" class="btn btn-primary">View Live Map</a>
            </div>
        </div>
    </div>
    {% endif %}

    {% if current_user.role == 'superadmin' %}
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5>User Management</h5>
            </div>
            <div class="card-body">
                <p>Create and manage user accounts.</p>
                <a href="{{ url_for('auth.create_user') }}" class="btn btn-success">Create User</a>
            </div>
        </div>
    </div>
    {% endif %}

    {% if current_user.role == 'customer' %}
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5>Your Vehicles</h5>
            </div>
            <div class="card-body">
                <p>View tracking information for your assigned vehicles.</p>
                <p class="text-muted">Vehicle tracking features will be available here.</p>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
