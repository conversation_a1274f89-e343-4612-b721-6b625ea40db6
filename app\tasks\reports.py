"""
Report generation and distribution tasks.
"""

import os
import io
import csv
import json
import logging
from datetime import datetime, timedelta, date
from typing import Dict, Any, List, Optional
import tempfile

from celery_app import celery_app
from app.extensions import db
from app.tracking.models import Vehicle, Location, Alert
from app.auth.models import User
from app.tasks.notifications import send_email_notification

logger = logging.getLogger(__name__)

@celery_app.task
def generate_daily_reports():
    """
    Generate daily reports for all active vehicles.
    """
    try:
        yesterday = date.today() - timedelta(days=1)
        start_time = datetime.combine(yesterday, datetime.min.time())
        end_time = datetime.combine(yesterday, datetime.max.time())
        
        # Get all active vehicles
        vehicles = Vehicle.query.filter(Vehicle.is_active == True).all()
        
        reports_generated = 0
        for vehicle in vehicles:
            report_result = generate_vehicle_report.delay(
                vehicle.id, 
                start_time.isoformat(), 
                end_time.isoformat(),
                'daily'
            )
            reports_generated += 1
        
        logger.info(f"Daily report generation initiated for {reports_generated} vehicles")
        return {"status": "success", "reports_initiated": reports_generated}
        
    except Exception as exc:
        logger.error(f"Failed to generate daily reports: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def generate_weekly_reports():
    """
    Generate weekly reports for all active vehicles.
    """
    try:
        # Get last week's date range
        today = date.today()
        last_monday = today - timedelta(days=today.weekday() + 7)
        last_sunday = last_monday + timedelta(days=6)
        
        start_time = datetime.combine(last_monday, datetime.min.time())
        end_time = datetime.combine(last_sunday, datetime.max.time())
        
        vehicles = Vehicle.query.filter(Vehicle.is_active == True).all()
        
        reports_generated = 0
        for vehicle in vehicles:
            report_result = generate_vehicle_report.delay(
                vehicle.id,
                start_time.isoformat(),
                end_time.isoformat(),
                'weekly'
            )
            reports_generated += 1
        
        logger.info(f"Weekly report generation initiated for {reports_generated} vehicles")
        return {"status": "success", "reports_initiated": reports_generated}
        
    except Exception as exc:
        logger.error(f"Failed to generate weekly reports: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def generate_vehicle_report(vehicle_id: int, start_date: str, end_date: str, 
                           report_type: str = 'custom'):
    """
    Generate comprehensive report for a specific vehicle.
    
    Args:
        vehicle_id: ID of the vehicle
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
        report_type: Type of report ('daily', 'weekly', 'monthly', 'custom')
    """
    try:
        vehicle = Vehicle.query.get(vehicle_id)
        if not vehicle:
            return {"status": "failed", "error": "Vehicle not found"}
        
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # Get locations for the period
        locations = Location.query.filter(
            Location.vehicle_id == vehicle_id,
            Location.timestamp >= start_dt,
            Location.timestamp <= end_dt,
            Location.is_valid == True
        ).order_by(Location.timestamp).all()
        
        # Get alerts for the period
        alerts = Alert.query.filter(
            Alert.vehicle_id == vehicle_id,
            Alert.created_at >= start_dt,
            Alert.created_at <= end_dt
        ).all()
        
        # Calculate statistics
        stats = calculate_report_statistics(locations)
        
        # Generate report data
        report_data = {
            "vehicle": {
                "id": vehicle.id,
                "name": vehicle.name,
                "license_plate": vehicle.license_plate
            },
            "period": {
                "start": start_date,
                "end": end_date,
                "type": report_type
            },
            "statistics": stats,
            "alerts": [
                {
                    "type": alert.alert_type,
                    "title": alert.title,
                    "message": alert.message,
                    "created_at": alert.created_at.isoformat()
                }
                for alert in alerts
            ],
            "locations_summary": {
                "total_points": len(locations),
                "first_location": locations[0].timestamp.isoformat() if locations else None,
                "last_location": locations[-1].timestamp.isoformat() if locations else None
            },
            "generated_at": datetime.utcnow().isoformat()
        }
        
        # Save report (in a real implementation, you might save to file storage)
        report_filename = f"vehicle_report_{vehicle.license_plate}_{report_type}_{start_dt.strftime('%Y%m%d')}.json"
        
        logger.info(f"Report generated for vehicle {vehicle.license_plate}: {report_type}")
        return {
            "status": "success",
            "vehicle_id": vehicle_id,
            "report_type": report_type,
            "filename": report_filename,
            "statistics": stats
        }
        
    except Exception as exc:
        logger.error(f"Failed to generate vehicle report: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def generate_fleet_summary_report(start_date: str, end_date: str):
    """
    Generate fleet-wide summary report.
    
    Args:
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
    """
    try:
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # Get all active vehicles
        vehicles = Vehicle.query.filter(Vehicle.is_active == True).all()
        
        fleet_stats = {
            "total_vehicles": len(vehicles),
            "active_vehicles": 0,
            "total_distance": 0.0,
            "total_alerts": 0,
            "vehicle_summaries": []
        }
        
        for vehicle in vehicles:
            # Get locations for this vehicle
            locations = Location.query.filter(
                Location.vehicle_id == vehicle.id,
                Location.timestamp >= start_dt,
                Location.timestamp <= end_dt,
                Location.is_valid == True
            ).order_by(Location.timestamp).all()
            
            # Get alerts for this vehicle
            alerts_count = Alert.query.filter(
                Alert.vehicle_id == vehicle.id,
                Alert.created_at >= start_dt,
                Alert.created_at <= end_dt
            ).count()
            
            if locations:
                fleet_stats["active_vehicles"] += 1
                vehicle_stats = calculate_report_statistics(locations)
                fleet_stats["total_distance"] += vehicle_stats.get("total_distance_km", 0)
                fleet_stats["total_alerts"] += alerts_count
                
                fleet_stats["vehicle_summaries"].append({
                    "vehicle_id": vehicle.id,
                    "name": vehicle.name,
                    "license_plate": vehicle.license_plate,
                    "distance_km": vehicle_stats.get("total_distance_km", 0),
                    "locations_count": len(locations),
                    "alerts_count": alerts_count,
                    "max_speed": vehicle_stats.get("max_speed_kmh", 0),
                    "avg_speed": vehicle_stats.get("avg_speed_kmh", 0)
                })
        
        # Calculate fleet averages
        if fleet_stats["active_vehicles"] > 0:
            fleet_stats["avg_distance_per_vehicle"] = fleet_stats["total_distance"] / fleet_stats["active_vehicles"]
            fleet_stats["avg_alerts_per_vehicle"] = fleet_stats["total_alerts"] / fleet_stats["active_vehicles"]
        
        report_data = {
            "report_type": "fleet_summary",
            "period": {"start": start_date, "end": end_date},
            "fleet_statistics": fleet_stats,
            "generated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"Fleet summary report generated for {fleet_stats['active_vehicles']} vehicles")
        return {"status": "success", "report_data": report_data}
        
    except Exception as exc:
        logger.error(f"Failed to generate fleet summary report: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def send_weekly_reports():
    """
    Send weekly reports to users who have opted in.
    """
    try:
        # Get users who want weekly reports
        users = User.query.filter(
            User.is_active == True,
            User.weekly_reports == True,
            User.email_notifications == True
        ).all()
        
        # Generate last week's date range
        today = date.today()
        last_monday = today - timedelta(days=today.weekday() + 7)
        last_sunday = last_monday + timedelta(days=6)
        
        start_time = datetime.combine(last_monday, datetime.min.time())
        end_time = datetime.combine(last_sunday, datetime.max.time())
        
        # Generate fleet summary
        fleet_report_result = generate_fleet_summary_report.delay(
            start_time.isoformat(),
            end_time.isoformat()
        )
        
        reports_sent = 0
        for user in users:
            # Send weekly summary email
            subject = f"Ventsys Weekly Report - {last_monday.strftime('%B %d')} to {last_sunday.strftime('%B %d, %Y')}"
            
            body = f"""
Dear {user.full_name or user.email},

Here is your weekly fleet tracking summary for {last_monday.strftime('%B %d')} to {last_sunday.strftime('%B %d, %Y')}:

[Fleet Summary would be included here]

You can view detailed reports by logging into the Ventsys system.

Best regards,
Ventsys Fleet Tracking Team

---
To unsubscribe from weekly reports, please update your notification preferences in your profile.
            """
            
            email_result = send_email_notification.delay(user.email, subject, body)
            reports_sent += 1
        
        logger.info(f"Weekly reports sent to {reports_sent} users")
        return {"status": "success", "reports_sent": reports_sent}
        
    except Exception as exc:
        logger.error(f"Failed to send weekly reports: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

@celery_app.task
def export_vehicle_data_csv(vehicle_id: int, start_date: str, end_date: str):
    """
    Export vehicle location data to CSV format.
    
    Args:
        vehicle_id: ID of the vehicle
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
    """
    try:
        vehicle = Vehicle.query.get(vehicle_id)
        if not vehicle:
            return {"status": "failed", "error": "Vehicle not found"}
        
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # Get locations
        locations = Location.query.filter(
            Location.vehicle_id == vehicle_id,
            Location.timestamp >= start_dt,
            Location.timestamp <= end_dt
        ).order_by(Location.timestamp).all()
        
        # Create CSV content
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Timestamp', 'Latitude', 'Longitude', 'Speed (km/h)', 
            'Heading', 'Accuracy (m)', 'Source', 'Valid'
        ])
        
        # Write data
        for location in locations:
            writer.writerow([
                location.timestamp.isoformat(),
                location.lat,
                location.lng,
                location.speed or '',
                location.heading or '',
                location.accuracy or '',
                location.source or 'gps',
                'Yes' if location.is_valid else 'No'
            ])
        
        csv_content = output.getvalue()
        output.close()
        
        # In a real implementation, you would save this to file storage
        filename = f"{vehicle.license_plate}_data_{start_dt.strftime('%Y%m%d')}_{end_dt.strftime('%Y%m%d')}.csv"
        
        logger.info(f"CSV export completed for vehicle {vehicle.license_plate}")
        return {
            "status": "success",
            "filename": filename,
            "records_exported": len(locations),
            "csv_size": len(csv_content)
        }
        
    except Exception as exc:
        logger.error(f"Failed to export vehicle data to CSV: {str(exc)}")
        return {"status": "failed", "error": str(exc)}

def calculate_report_statistics(locations: List[Location]) -> Dict[str, Any]:
    """
    Calculate statistics from a list of locations.
    
    Args:
        locations: List of Location objects
        
    Returns:
        Dictionary containing calculated statistics
    """
    if not locations:
        return {
            "total_distance_km": 0,
            "total_time_hours": 0,
            "max_speed_kmh": 0,
            "avg_speed_kmh": 0,
            "locations_count": 0
        }
    
    total_distance = 0.0
    speeds = []
    
    # Calculate distance and collect speeds
    for i in range(1, len(locations)):
        prev_loc = locations[i-1]
        curr_loc = locations[i]
        
        # Simple distance calculation (should use proper geospatial calculation in production)
        lat_diff = curr_loc.lat - prev_loc.lat
        lng_diff = curr_loc.lng - prev_loc.lng
        distance = ((lat_diff ** 2) + (lng_diff ** 2)) ** 0.5 * 111  # Rough km conversion
        total_distance += distance
        
        if curr_loc.speed is not None:
            speeds.append(curr_loc.speed)
    
    # Calculate time duration
    total_time_seconds = (locations[-1].timestamp - locations[0].timestamp).total_seconds()
    total_time_hours = total_time_seconds / 3600
    
    return {
        "total_distance_km": round(total_distance, 2),
        "total_time_hours": round(total_time_hours, 2),
        "max_speed_kmh": round(max(speeds), 1) if speeds else 0,
        "avg_speed_kmh": round(sum(speeds) / len(speeds), 1) if speeds else 0,
        "locations_count": len(locations),
        "first_timestamp": locations[0].timestamp.isoformat(),
        "last_timestamp": locations[-1].timestamp.isoformat()
    }
