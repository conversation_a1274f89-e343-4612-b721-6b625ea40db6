"""
Comprehensive logging configuration for the Ventsys application.

This module provides structured logging with:
- Multiple log levels and handlers
- Request/response logging
- Error tracking with context
- Log rotation and archival
- Performance monitoring
- Security event logging
"""

import os
import sys
import logging
import logging.handlers
from datetime import datetime
from typing import Dict, Any, Optional
import json
import traceback
from flask import request, g, current_app
import time

class J<PERSON><PERSON>ormatter(logging.Formatter):
    """Custom JSON formatter for structured logging."""
    
    def format(self, record):
        """Format log record as JSON."""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'process': record.process
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add extra fields if present
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        # Add request context if available
        try:
            if request:
                log_entry['request'] = {
                    'method': request.method,
                    'url': request.url,
                    'remote_addr': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent', ''),
                    'request_id': getattr(g, 'request_id', None)
                }
                
                # Add user context if authenticated
                from flask_login import current_user
                if current_user and current_user.is_authenticated:
                    log_entry['user'] = {
                        'id': current_user.id,
                        'email': current_user.email,
                        'role': current_user.role
                    }
        except RuntimeError:
            # Outside request context
            pass
        
        return json.dumps(log_entry, default=str)

class ContextFilter(logging.Filter):
    """Filter to add context information to log records."""
    
    def filter(self, record):
        """Add context information to the log record."""
        # Add request ID if available
        if hasattr(g, 'request_id'):
            record.request_id = g.request_id
        
        # Add performance metrics if available
        if hasattr(g, 'start_time'):
            record.request_duration = time.time() - g.start_time
        
        return True

class SecurityFilter(logging.Filter):
    """Filter for security-related events."""
    
    SECURITY_EVENTS = [
        'login_attempt', 'login_failure', 'logout',
        'permission_denied', 'invalid_token',
        'suspicious_activity', 'rate_limit_exceeded'
    ]
    
    def filter(self, record):
        """Only pass security-related log records."""
        return any(event in record.getMessage().lower() for event in self.SECURITY_EVENTS)

def setup_logging(app):
    """
    Set up comprehensive logging for the Flask application.
    
    Args:
        app: Flask application instance
    """
    # Get configuration
    log_level = getattr(logging, app.config.get('LOG_LEVEL', 'INFO').upper())
    log_file = app.config.get('LOG_FILE', 'ventsys.log')
    log_dir = app.config.get('LOG_DIR', 'logs')
    
    # Create logs directory if it doesn't exist
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler with colored output for development
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    
    if app.debug:
        # Colored formatter for development
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    else:
        # JSON formatter for production
        console_formatter = JSONFormatter()
    
    console_handler.setFormatter(console_formatter)
    console_handler.addFilter(ContextFilter())
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, log_file),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=10
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(JSONFormatter())
    file_handler.addFilter(ContextFilter())
    root_logger.addHandler(file_handler)
    
    # Error file handler for errors and above
    error_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'error.log'),
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(JSONFormatter())
    error_handler.addFilter(ContextFilter())
    root_logger.addHandler(error_handler)
    
    # Security log handler
    security_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'security.log'),
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=10
    )
    security_handler.setLevel(logging.WARNING)
    security_handler.setFormatter(JSONFormatter())
    security_handler.addFilter(SecurityFilter())
    security_handler.addFilter(ContextFilter())
    root_logger.addHandler(security_handler)
    
    # Performance log handler
    performance_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'performance.log'),
        maxBytes=5 * 1024 * 1024,  # 5MB
        backupCount=5
    )
    performance_handler.setLevel(logging.INFO)
    performance_handler.setFormatter(JSONFormatter())
    performance_handler.addFilter(ContextFilter())
    
    # Create performance logger
    performance_logger = logging.getLogger('performance')
    performance_logger.addHandler(performance_handler)
    performance_logger.propagate = False
    
    # Configure specific loggers
    configure_specific_loggers(app)
    
    app.logger.info("Logging system initialized successfully")

def configure_specific_loggers(app):
    """Configure specific loggers for different components."""
    
    # SQLAlchemy logger
    sqlalchemy_logger = logging.getLogger('sqlalchemy.engine')
    if app.debug:
        sqlalchemy_logger.setLevel(logging.INFO)
    else:
        sqlalchemy_logger.setLevel(logging.WARNING)
    
    # Werkzeug logger (Flask development server)
    werkzeug_logger = logging.getLogger('werkzeug')
    werkzeug_logger.setLevel(logging.WARNING)
    
    # Celery logger
    celery_logger = logging.getLogger('celery')
    celery_logger.setLevel(logging.INFO)
    
    # Redis logger
    redis_logger = logging.getLogger('redis')
    redis_logger.setLevel(logging.WARNING)

def log_request_start():
    """Log the start of a request."""
    g.start_time = time.time()
    g.request_id = generate_request_id()
    
    current_app.logger.info(
        "Request started",
        extra={
            'extra_fields': {
                'event_type': 'request_start',
                'method': request.method,
                'url': request.url,
                'remote_addr': request.remote_addr,
                'user_agent': request.headers.get('User-Agent', ''),
                'content_length': request.content_length,
                'request_id': g.request_id
            }
        }
    )

def log_request_end(response):
    """Log the end of a request."""
    duration = time.time() - getattr(g, 'start_time', time.time())
    
    # Log to main logger
    current_app.logger.info(
        "Request completed",
        extra={
            'extra_fields': {
                'event_type': 'request_end',
                'status_code': response.status_code,
                'duration_ms': round(duration * 1000, 2),
                'response_size': len(response.get_data()),
                'request_id': getattr(g, 'request_id', None)
            }
        }
    )
    
    # Log to performance logger for slow requests
    if duration > 1.0:  # Log requests taking more than 1 second
        performance_logger = logging.getLogger('performance')
        performance_logger.warning(
            f"Slow request: {request.method} {request.url} took {duration:.2f}s",
            extra={
                'extra_fields': {
                    'event_type': 'slow_request',
                    'duration_ms': round(duration * 1000, 2),
                    'method': request.method,
                    'url': request.url,
                    'status_code': response.status_code
                }
            }
        )
    
    return response

def log_security_event(event_type: str, message: str, **kwargs):
    """
    Log a security-related event.
    
    Args:
        event_type: Type of security event
        message: Event message
        **kwargs: Additional context
    """
    security_logger = logging.getLogger('security')
    security_logger.warning(
        f"Security event: {event_type} - {message}",
        extra={
            'extra_fields': {
                'event_type': 'security_event',
                'security_event_type': event_type,
                **kwargs
            }
        }
    )

def log_performance_metric(metric_name: str, value: float, unit: str = 'ms', **kwargs):
    """
    Log a performance metric.
    
    Args:
        metric_name: Name of the metric
        value: Metric value
        unit: Unit of measurement
        **kwargs: Additional context
    """
    performance_logger = logging.getLogger('performance')
    performance_logger.info(
        f"Performance metric: {metric_name} = {value}{unit}",
        extra={
            'extra_fields': {
                'event_type': 'performance_metric',
                'metric_name': metric_name,
                'metric_value': value,
                'metric_unit': unit,
                **kwargs
            }
        }
    )

def generate_request_id() -> str:
    """Generate a unique request ID."""
    import uuid
    return str(uuid.uuid4())[:8]

def setup_request_logging(app):
    """Set up request/response logging middleware."""
    
    @app.before_request
    def before_request():
        log_request_start()
    
    @app.after_request
    def after_request(response):
        return log_request_end(response)
    
    @app.teardown_request
    def teardown_request(exception):
        if exception:
            current_app.logger.error(
                f"Request failed with exception: {str(exception)}",
                exc_info=True,
                extra={
                    'extra_fields': {
                        'event_type': 'request_exception',
                        'exception_type': type(exception).__name__,
                        'request_id': getattr(g, 'request_id', None)
                    }
                }
            )
